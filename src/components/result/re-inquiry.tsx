'use client'

import {useRouter} from "next/navigation";
import toast from "react-hot-toast";
import useInquiry from "@/lib/hooks/useInquiry";
import {InquiryQueryParams, VehiclePaymentDetails} from "@/lib/types";
import CustomButton from "@/components/common/custom-button";
import ReInquiryModal from "@/components/result/re-inquiry-modal";

type Props = {
    data: VehiclePaymentDetails,
    isNew?: 'true' | 'false'
    newInquiryUrl: string
    inquiryPath?: string
}

export default function ReInquire({data, isNew, newInquiryUrl, inquiryPath}: Props) {
    const {mutate, isLoading} = useInquiry();
    const router = useRouter()

    async function handleReInquiry() {
        if (isLoading) return
        const queryParams: InquiryQueryParams = {
            newInquiry: 'true',
            withDetails: data.withDetails ? 'true' : 'false',
            phoneNumber: data?.phoneNumber,
            nationalCode: data?.nationalId,
            left: data.plaque.left,
            right: data.plaque?.right,
            middle: data.plaque?.middle,
            alphabet: data.plaque?.alphabet,
            isMotor: data.isMotor ? 'true' : 'false',
            inquiryType: data.inquiryType,
            inquiryPath: inquiryPath
        }

        const mutateResult = await mutate(queryParams);
        if (!mutateResult.success && mutateResult.href) {
            router.push(mutateResult.href)
        } else if (!mutateResult.success && mutateResult.message) {
            toast.error(mutateResult.message);
        } else if (mutateResult.success && mutateResult.href) {
            router.replace(mutateResult.href)
        }
    }

    async function onReInquireConfirmed() {
        await handleReInquiry()
    }

    return (
        <div className='w-full flex justify-between items-center'>
            {(isNew && isNew === 'true') ? (
                <CustomButton
                    variants='blue'
                    href={newInquiryUrl}
                    className='mt-2 py-4'>استعلام
                    جدید</CustomButton>

            ) : (
                <ReInquiryModal
                    isLoading={isLoading}
                    newInquiryUrl={newInquiryUrl}
                    onReInquirySelected={onReInquireConfirmed}
                />
            )
            }
        </div>
    );
}
