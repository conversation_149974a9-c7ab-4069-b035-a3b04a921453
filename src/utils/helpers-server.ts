import cookieService from "@/lib/services/cookie.service";
import tokenService from "@/lib/services/token.service";
import {
    ActionResult, BaseParamsType,
    GenericResponse,
    InquiryType,
    InquiryTypeEnum,
    JWTUserPayload, PlaqueType, TransactionType,
} from "@/lib/types";
import {BadRequestError, HttpError, InternalServerError, NotEnoughForPaymentError, ValidationError} from "@/lib/error";
import status from "http-status";
import crypto from 'crypto';
import {
    jibitAccountToIbanSchema, IbanInquirySchema,
    iranBankCardNumberSchema,
    iranMobileSchema, sayadiNumberSchema, ghabzinoAccountToIbanSchema, GhabzinoCardToIbanServerSchema,
} from "@/lib/zod-schemas";
import {isValidIranianNationalCode, plateNumberIsNotValid} from "@/utils/validators";
import {ClientSession} from "mongoose";
import userRepository from "@/features/user/user.repository";
import walletRepository from "@/features/wallet/wallet.repository";
import {headers} from "next/headers";
import {X_SOURCE} from "@/lib/constants";

export async function getCurrentUser() {

    const tokenFromCookie = await cookieService.getAuthorizationToken()

    if (!tokenFromCookie) return null;

    const accessToken = tokenFromCookie.split(" ")[1]

    if (!accessToken) return null;

    return tokenService.verifyJwtToken<JWTUserPayload>(accessToken);
}

export function handleActionErrorResponse(error: any): ActionResult<any> {
    console.error('Error:', error);

    if (error instanceof HttpError) {
        return {
            success: false,
            message: error.message,
            status: error.statusCode,
            errors: error instanceof ValidationError ? error.errors : undefined
        };
    }

    return {
        success: false,
        message: 'خطای غیرمنتظره‌ای رخ داده است',
        status: status.INTERNAL_SERVER_ERROR,
        errors: ['خطای داخلی سرور']
    };
}

export function remainingTimeInSeconds(date: Date) {
    return Math.ceil((date.getTime() - new Date().getTime()) / 1000)
}

export function convertPlaquePartsToString({left, right, middle, alphabet}: PlaqueType) {
    return `${left}-${alphabet ? alphabet + '-' : ''}${middle ? middle + '-' : ''}${right}`;
}

export function handleZodValidationResult(result: any): void {
    if (result && result.success === false) {
        const errors = result.error.errors.map((e: { message: string }) => e.message);
        throw new ValidationError(errors);
    }
}

export function handleGhabzinoTrafficFineInquiryValidation(params: InquiryType) {
    const {left, right, middle, alphabet, isMotor, withDetails, nationalCode, phoneNumber} = params;
    const errorMessages: string[] = []
    const isPlaqueNotValid = plateNumberIsNotValid({
        left: left!,
        right: right!,
        middle,
        alphabet
    }, isMotor === 'true')

    if (isPlaqueNotValid) {
        errorMessages.push("شماره پلاک را صحیح وارد کنید")
    }

    if (withDetails === 'true') {
        const mobileResult = iranMobileSchema.safeParse(phoneNumber);
        if (!mobileResult.success) {
            errorMessages.push("شماره موبایل را صحیح وارد کنید")
        }
        const isNationalIdValid = isValidIranianNationalCode(nationalCode)
        if (!isNationalIdValid) {
            errorMessages.push("کد ملی را صحیح وارد کنید")
        }
    }

    if (errorMessages.length > 0) {
        throw new ValidationError(errorMessages);
    }
}

export function handleActionError(error: any): ActionResult<any> {
    return {
        success: false,
        message: error.message,
        status: error.status
    }
}


export function generateTrackingNumber(): string {
    const time = Date.now().toString();
    const random = crypto.randomBytes(4).toString('hex');
    const hash = crypto.createHash('md5').update(time + random).digest('hex').slice(0, 6);
    const pre = "INQ"
    return `${pre}-${time}-${hash}`;
}

export function addHoursToNow(hours: number): Date {
    const now = new Date();
    now.setHours(now.getHours() + hours);
    return now;
}

export function handleError<T = void>(error: any, location?: string, showDefaultMessageForInternalError = true): T {
    console.error(`خطا ${location ? location : ''}`, error);
    console.log(error.message);

    const defaultMessage = 'مشکلی پیش آمده است. لطفا بعدا دوباره تلاش کنید.';

    if (error instanceof HttpError) {
        return {
            success: false,
            message: (showDefaultMessageForInternalError && error.statusCode === status.INTERNAL_SERVER_ERROR) ? defaultMessage : error.message,
            errors: (error as any)?.errors
        } as T
    }

    if (error.response && error.response.status === status.INTERNAL_SERVER_ERROR) {
        return {
            success: false,
            message: defaultMessage, // Default error message for 500
        } as T;
    }

    const errorMessage = (error instanceof Error) ? error.message : defaultMessage;
    return {
        success: false,
        message: errorMessage,
    } as T
}

export async function handleBalanceOperation({
                                                 userId,
                                                 amount,
                                                 reason,
                                                 phone,
                                                 referenceId,
                                                 refundId,
                                                 referenceType,
                                                 source
                                             }: {
    amount: number,
    reason: string
    referenceId?: string,
    refundId?: string,
    referenceType?: TransactionType

} & BaseParamsType, session: ClientSession) {

    const newBalance = await userRepository.adjustBalance({
        userId,
        amount,
        source
    }, session)

    if (newBalance === null) {
        throw new NotEnoughForPaymentError("موجودی کافی برای انجام عملیات وجود ندارد")
    }

    const walletResult = await walletRepository.createEntry({
        amount,
        phone,
        refundId,
        source,
        balanceAfter: newBalance,
        reason,
        userId,
        referenceId,
        referenceType
    }, session)

    return {walletResult, balance: newBalance}
}

export function handleInquiryServiceError(error: any, location?: string, showDefaultMessageForInternalError = true): GenericResponse {
    console.error(`خطا ${location ? location : ''}`, error);
    console.log(error.message);

    const defaultMessage = 'مشکلی پیش آمده است. لطفا بعدا دوباره تلاش کنید.';

    if (error instanceof HttpError) {
        return {
            success: 'FAILED',
            message: (showDefaultMessageForInternalError && error.statusCode === status.INTERNAL_SERVER_ERROR) ? defaultMessage : error.message,
            errors: (error as any)?.errors
        }
    }

    if (error.response && error.response.status === status.INTERNAL_SERVER_ERROR) {
        return {
            success: 'FAILED',
            message: defaultMessage,
        }
    }

    const errorMessage = (error instanceof Error) ? error.message : defaultMessage;
    return {
        success: 'FAILED',
        message: errorMessage,
    }
}

export function convertRialToToman(amount: number) {
    return amount / 10
}

export function miladiToShamsi(value: string) {
    const date = new Date(value);
    const shamsiDate = new Intl.DateTimeFormat('fa-IR', {
        dateStyle: 'short',
        timeStyle: 'short',
        hour12: false,
    }).format(date);
    return shamsiDate;
}

export async function getHost() {
    const headersList = await headers()
    const host = headersList.get('host');
    return host
}

export function handleInquiryValidation(data: InquiryType) {
    const {inquiryType, cardNumber, depositNumber, bank, sayadId, iban, accountTypeName} = data;
    switch (inquiryType) {
        case InquiryTypeEnum.Jibit_CardToDeposit:
        case InquiryTypeEnum.Jibit_CardToIban:
        case InquiryTypeEnum.Jibit_CardInquiry:
            handleZodValidationResult(iranBankCardNumberSchema.safeParse(cardNumber));
            break;
        case InquiryTypeEnum.Jibit_DepositToIban:
            handleZodValidationResult(jibitAccountToIbanSchema.safeParse({bank, depositNumber}));
            break;
        case InquiryTypeEnum.Ghanzino_DepositToIban:
            handleZodValidationResult(ghabzinoAccountToIbanSchema.safeParse({bank, depositNumber, accountTypeName}));
            break;
        case InquiryTypeEnum.Ghanzino_CardToIban:
            handleZodValidationResult(GhabzinoCardToIbanServerSchema.safeParse({cardNumber, accountTypeName}));
            break;
        case InquiryTypeEnum.Ghabzino_SayadCheckInquiry:
            handleZodValidationResult(sayadiNumberSchema.safeParse({sayadId}));
            break;
        case InquiryTypeEnum.Jibit_IbanInquiry:
            handleZodValidationResult(IbanInquirySchema.safeParse({iban}));
            break;
        case InquiryTypeEnum.Ghanzino_KhalafiKhodro:
            handleGhabzinoTrafficFineInquiryValidation(data)
            break;
        case InquiryTypeEnum.Ghanzino_KhalafiMotor:
            handleGhabzinoTrafficFineInquiryValidation(data)
            break;
        default:
            console.error("نوع استعلام نامعتبر است");
            throw new BadRequestError('مقدار type نامعتبر است')
    }
}

export function mapBankCodes(originalCode: string): string | undefined {

    const bankCodeMapping: Record<string, string> = {
        "MARKAZI": "Markazi",  // Central Bank of the Islamic Republic of Iran -> Markazi
        "SANAT_VA_MADAN": "SanatoMadan", // Bank of Industry & Mine -> SanatoMadan
        "MELLAT": "Mellat",   // Bank Mellat -> Mellat
        "REFAH": "RefaheKargaran",    // Refah K. Bank -> RefaheKargaran
        "MASKAN": "Maskan",   // Bank Maskan -> Maskan
        "SEPAH": "Sepah",    // Bank Sepah -> Sepah
        "KESHAVARZI": "Keshavarzi", // Bank Keshavarzi Iran -> Keshavarzi
        "MELLI": "Mellielran",    // Bank Melli Iran -> Mellielran
        "TEJARAT": "Tejarat",  // Tejarat Bank -> Tejarat
        "SADERAT": "Saderatelran",  // Bank Saderat Iran -> Saderatelran
        "TOSEAH_SADERAT": "ToseeSaderatelran", // Export Development Bank of Iran -> ToseeSaderatelran
        "POST": "PostBankelran",     // Post Bank Iran -> PostBankelran
        "TOSEAH_TAAVON": "ToseeTaavon", // Tose'e Ta'avon Bank -> ToseeTaavon
        "KARAFARIN": "KarAfarin", // Karafarin Bank -> KarAfarin
        "PARSIAN": "Parsian",   // Parsian Bank -> Parsian
        "EGHTESAD_NOVIN": "EghtesadeNovin", // Bank Eghtesad Novin -> EghtesadeNovin
        "SAMAN": "Saman",    // Saman Bank -> Saman
        "PASARGAD": "Pasargad",  // Bank Pasargad -> Pasargad
        "SARMAYEH": "Sarmaye", // Sarmayeh Bank -> Sarmaye
        "SINA": "Sina",     // Sina Bank -> Sina
        "MEHR_IRAN": "Mehriran", // Gharzolhasane Mehr Iran Bank -> Mehriran
        "SHAHR": "Shahr",    // Shahr Bank -> Shahr
        "AYANDEH": "Ayande",   // Ayandeh Bank -> Ayande
        "GARDESHGARI": "Gardeshgari", // Tourism Bank -> Gardeshgari
        "DAY": "Dey",       // Day Bank -> Dey
        "IRANZAMIN": "IranZamin", // Iran Zamin Bank -> IranZamin
        "RESALAT": "Resalat",  // Resalat Gharzolhasane Bank -> Resalat
        "MELAL": "MelalAskarie",    // Melal Credit Institution -> MelalAskarie
        "KHAVARMIANEH": "KhavareMiane", // Middle East Bank -> Khavare Miane
    };

    // Return the mapped Ghabzino bank name, or undefined if not found
    return bankCodeMapping[originalCode];
}

export async function getAppSource() {
    const headersList = await headers()
    const source = headersList.get(X_SOURCE)

    if (!source) throw new InternalServerError('tenantId یافت نشد')

    return source
}

