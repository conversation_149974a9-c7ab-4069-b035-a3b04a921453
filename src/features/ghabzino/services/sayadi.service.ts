import {
    BaseParamsType,
    GenericResponse,
    GhabzinoResponse,
    GhabzinoSayadiInquiryResult,
    IInquiryService, InquiryStatus, InquiryTypeEnum, ProviderType,
    ServiceResult
} from "@/lib/types";
import inquiryRepository from "@/features/inquiry/inquiry.repository";
import {InternalServerError} from "@/lib/error";
import {handleInquiryServiceError} from "@/utils/helpers-server";
import {ClientSession} from "mongoose";
import GhabzinoFetchclient from "@/features/ghabzino/ghabzino.fetchClient";

type SayadCheckInquiryInput = {
    sayadId: string
} & BaseParamsType

class SayadiService implements IInquiryService<SayadCheckInquiryInput, ServiceResult | undefined> {

    async lookup({sayadId, validPeriod, userId, phone, source, balance, balance_after}: {
        sayadId: string
    } & BaseParamsType, session: ClientSession | null = null): Promise<GenericResponse<ServiceResult | undefined>> {
        try {

            const createBaseParams: any = {
                sayadId,
                source,
                user: userId,
                provider: ProviderType.GHABZINO,
                phone,
                balance,
                inquiryType: InquiryTypeEnum.Ghabzino_SayadCheckInquiry,

            }

            let response: GhabzinoResponse<GhabzinoSayadiInquiryResult> | null = null
            try {
                response = await GhabzinoFetchclient.sayadCheckInquiry(sayadId);

            } catch (error: any) {
                await inquiryRepository.create({
                    ...createBaseParams,
                    inquiryStatus: InquiryStatus.FAILED,
                    inquiryResult: error?.message ? error.message : error,
                }, session);

                return {
                    success: "FAILED",
                    message: 'استعلام شما ناموفق بود لطفا مجددا تلاش کنید'
                }
            }

            const successCodes = ['G00000', 'GD2621', 'GD2627'];
            const success = successCodes.includes(response?.Status?.Code);

            const savedResult = await inquiryRepository.create<GhabzinoResponse<GhabzinoSayadiInquiryResult>>({
                ...createBaseParams,
                inquiryStatus: success ? InquiryStatus.SUCCESS : InquiryStatus.FAILED,
                validPeriod: success ? validPeriod : undefined,
                inquiryResult: response,
                paid: success,
                balance_after: success ? balance_after : undefined,
            }, session);

            if (!savedResult) {
                throw new InternalServerError();
            }

            if (success) {
                return {
                    success: 'SUCCESS',
                    data: {
                        id: savedResult._id.toString(),
                        trackingNumber: savedResult.trackingNumber!,
                    }
                };
            }

            return {
                success: 'FAILED',
                message: response?.Status?.Description || 'خطای ناشناخته‌ای رخ داده است',
            };

        } catch (error: any) {
            return handleInquiryServiceError(error);
        }
    }
}

export default SayadiService;