'use client';

import React, {createContext, useEffect, useState, useCallback} from 'react';
import {Category} from '@/lib/types';
import toast from "react-hot-toast";
import {getOrSeedAppCategories} from "@/features/category/category.action";

type ServiceContextType = {
    categories: Category[];
    isLoading: boolean;
    refetchCategories: () => Promise<void>;
};

export const CategoryContext = createContext<ServiceContextType>({
    categories: [],
    isLoading: true,
    refetchCategories: async () => {
    },
});

type ServiceProviderProps = {
    children: React.ReactNode;
    initialCategories?: Category[]; // داده اولیه از سرور
};

export const CategoryProvider = ({children, initialCategories = []}: ServiceProviderProps) => {
    const [categories, setCategories] = useState<Category[]>(initialCategories);
    const [isLoading, setIsLoading] = useState(false);

    const fetchCategories = useCallback(async () => {
        setIsLoading(true)
        const actionResult = await getOrSeedAppCategories();
        if (!actionResult.success) {
            toast.error(actionResult.message!);
        } else {
            setCategories(actionResult.data!);
        }
        setIsLoading(false);
    }, []);

    useEffect(() => {
        if (initialCategories.length === 0) {
            fetchCategories();
        }
    }, [fetchCategories, initialCategories.length]);

    return (
        <CategoryContext.Provider value={{categories: categories, isLoading, refetchCategories: fetchCategories}}>
            {children}
        </CategoryContext.Provider>
    );
};