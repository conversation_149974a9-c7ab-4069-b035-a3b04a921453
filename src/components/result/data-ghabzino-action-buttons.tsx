'use client'

import CustomButton from "@/components/common/custom-button";
import {Check, Copy, Download} from "lucide-react";
import {GhabzinoBankType} from "@/lib/types";
import {useState} from "react";

type Props = {
    result: GhabzinoBankType,
}

export default function DataGhabzinoActionButtons({result}: Props) {
    const [copied, setCopied] = useState(false)


    function generateText(result: GhabzinoBankType) {
        let text = ''
        text += result?.bankShowName ? `نام بانک: ${result?.bankShowName}\n` : '';
        if (result?.extraInfo) {
            Object.entries(result.extraInfo).map(([key, value]) => {
                text += `${key}: ${value}\n`
            });
        }
        text += result?.depositNumber ? `شماره حساب: ${result.depositNumber}\n` : '';
        text += result?.shebaNumber ? `شماره شبا: ${result.shebaNumber}\n` : '';

        return text;
    }

    const copyAll = async (result: GhabzinoBankType) => {

        const text = generateText(result);
        try {
            await navigator.clipboard.writeText(text);
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        } catch (err) {
            console.error("Failed to copy text: ", err);
        }
    }

    function downloadTextFile(result: GhabzinoBankType, filename = 'info.txt') {
        const text = generateText(result);
        const blob = new Blob([text], {type: 'text/plain'});
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    return (
        <div className="grid grid-cols-2 gap-4 mt-8">
            <CustomButton
                variants='blue'
                onClick={() => copyAll(result)}
            >
                {copied ? <Check className="h-5 w-5"/> : <Copy className="h-5 w-5"/>}
                <span className='mx-2'>{copied ? "کپی شد" : "کپی کردن همه"}</span>
            </CustomButton>
            <CustomButton
                variants='blue'
                onClick={() => downloadTextFile(result)}
            >
                <Download className="h-5 w-5"/>
                <span className='mx-2'>دانلود</span>
            </CustomButton>
        </div>
    );
}
