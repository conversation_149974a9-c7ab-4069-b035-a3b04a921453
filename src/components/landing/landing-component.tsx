import {Category} from "@/lib/types";
import {iconMap} from "@/components/landing/icons";
import ServiceLink from "@/components/landing/service-link";
import {cn} from "@/lib/utils";

type Props = {
    categories: Category[]
}

export default function LandingComponent({categories}: Props) {

    const categoryLength = categories.length;

    return (
        <div className="w-full max-w-4xl mx-auto py-12 px-4" dir="rtl">
            {/* Header */}
            <h1 className="text-center text-2xl md:text-3xl font-medium text-emerald-600 mb-16 font-['Vazirmatn']">
                دوست شما در خدمات آنلاین
            </h1>

            {/* Top row of services */}
            <div className={cn('grid grid-cols-2 sm:grid-cols-3 gap-8 mb-12', {
                [`md:grid-cols-${categoryLength}`]: categoryLength < 4,
                "md:grid-cols-4": categoryLength >= 4,
            })}>

                {
                    categories
                        .map((category) => {
                            const iconData = iconMap[category.slug]
                            return (
                                <ServiceLink
                                    key={category.slug}
                                    title={category.title}
                                    path={category.path}
                                    icon={iconData.renderIcon()}
                                    bgClass={iconData.className}
                                />
                            )
                        })
                }

                {/*<div className="flex flex-col items-center">*/}
                {/*    <div className="bg-orange-500 rounded-full p-5 w-20 h-20 flex items-center justify-center mb-4">*/}
                {/*        <FileCheck className="w-10 h-10 text-white"/>*/}
                {/*    </div>*/}
                {/*    <span className="text-center text-gray-800 font-['Vazirmatn']">استعلام چک صیادی</span>*/}
                {/*</div>*/}

                {/*<div className="flex flex-col items-center">*/}
                {/*    <div className="bg-blue-500 rounded-full p-5 w-20 h-20 flex items-center justify-center mb-4">*/}
                {/*        <Car className="w-10 h-10 text-white"/>*/}
                {/*    </div>*/}
                {/*    <span className="text-center text-gray-800 font-['Vazirmatn']">دریافت خلافی موتور</span>*/}
                {/*</div>*/}

                {/*<div className="flex flex-col items-center">*/}
                {/*    <div className="bg-blue-600 rounded-full p-5 w-20 h-20 flex items-center justify-center mb-4">*/}
                {/*        <Car className="w-10 h-10 text-white"/>*/}
                {/*    </div>*/}
                {/*    <span className="text-center text-gray-800 font-['Vazirmatn']">دریافت خلافی خودرو</span>*/}
                {/*</div>*/}

            </div>

            {/* Bottom row with single service */}
            {/*<div className="flex justify-center">*/}
            {/*    <div className="flex flex-col items-center">*/}
            {/*        <div className="bg-blue-500 rounded-full p-5 w-20 h-20 flex items-center justify-center mb-4">*/}
            {/*            <Shield className="w-10 h-10 text-white"/>*/}
            {/*        </div>*/}
            {/*        <span className="text-center text-gray-800 font-['Vazirmatn']">استعلام بیمه شخص ثالث</span>*/}
            {/*    </div>*/}
            {/*</div>*/}
        </div>
    )
}