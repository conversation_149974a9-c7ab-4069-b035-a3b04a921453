import InquiryComponent from "@/components/common/inquiry/inquiry-component";
import {CategorySlugEnum} from "@/lib/types";

type Props = {
    categorySlug: CategorySlugEnum;
    description: string,
    categoryTitle?: string,
}

export default function InquiryWrapperComponent({categorySlug, description, categoryTitle}: Props) {
    return (
        <main className="flex-1 flex flex-col items-center justify-center !px-2">
            <div className="w-full max-w-3xl">
                <h1 className="text-center text-lg md:text-2xl font-medium text-emerald-600 mb-10 md:mb-16">
                    {description}
                </h1>
                <InquiryComponent categorySlug={categorySlug} categoryTitle={categoryTitle}/>
            </div>
        </main>
    );
}
