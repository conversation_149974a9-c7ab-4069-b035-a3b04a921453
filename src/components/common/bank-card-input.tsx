'use client'

import React, {useEffect, useRef} from "react";
import {cn} from "@/lib/utils";

interface IranBankCardNumberProps {
    value: string[];
    onChange?: (value: string[]) => void;
    className?: string;
}

const IranBankCardNumber: React.FC<IranBankCardNumberProps> = ({
                                                                   value,
                                                                   onChange,
                                                                   className,
                                                               }) => {
    const inputsRef = useRef<Array<HTMLInputElement | null> | any>([null, null, null, null]);

    useEffect(() => {
        if (inputsRef.current[0]) {
            inputsRef.current[0].focus();
        }
    }, []);

    const handleChange = (index: number, newValue: string) => {
        if (/^\d{0,4}$/.test(newValue)) {
            const newCardNumber = [...value];
            newCardNumber[index] = newValue;
            onChange?.(newCardNumber);

            if (newValue.length === 4 && index < 3) {
                inputsRef.current[index + 1]?.focus();
            }
        }
    };

    const handleKeyDown = (index: number, event: React.KeyboardEvent<HTMLInputElement>) => {
        if (event.key === "Backspace" && !value[index] && index > 0) {
            inputsRef.current[index - 1]?.focus();
        }
    };


    return (
        <div className={cn("w-full left-direction flex justify-center space-x-2", className)}>
            {value.map((num, index) => (
                <input
                    key={index}
                    ref={(el) => (inputsRef.current[index] = el as any)}
                    type="text"
                    maxLength={4}
                    placeholder=" - - - - "
                    inputMode="numeric"
                    value={num}
                    onChange={(e) => handleChange(index, e.target.value)}
                    onKeyDown={(e) => handleKeyDown(index, e)}
                    className="w-[24%] left-direction text-center bg-transparent border rounded-md border-neutral-300
                    focus-visible:border-neutral-700 text-sm h-[45px] md:h-[50px] text-neutral-700 outline-none md:text-base tracking-widest"
                />
            ))}
        </div>
    );
};

export default IranBankCardNumber;