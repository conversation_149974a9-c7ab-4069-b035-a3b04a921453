import {Card, CardContent} from "@/components/ui/card"
import {Suspense} from "react";
import {PaymentResultContents} from "@/components/depo/result/payment-result-contents";
import Loading from "@/app/loading";

type Props = {
    Authority: string;
    Status: string;
}

export default async function PaymentResult(props: Props) {


    return (
        <div className="w-full flex justify-center min-h-screen p-4">
            <div className="w-full max-w-md">
                <div className="transition-all duration-300">
                    <Card className="overflow-hidden border-none shadow-xl">
                        <CardContent className="p-0">
                            <div
                                className={`p-8 text-gray-700 bg-gradient-to-b from-gray-300`}
                            >
                                <div className="flex flex-col items-center justify-center">
                                    <h1 className="mb-2 text-center text-xl font-bold">نتیجه عملیات</h1>
                                </div>
                            </div>
                            <Suspense
                                fallback={
                                    <div
                                        className='w-full p-6 h-[200px] border border-gray-200 rounded-md'>
                                        <Loading
                                            fullHeight={false}/>
                                    </div>}>
                                <PaymentResultContents {...props} />
                            </Suspense>
                        </CardContent>
                    </Card>
                </div>

            </div>
        </div>
    )
}