import {
    BankInquiryType,
    BaseParamsType,
    GenericResponse,
    IInquiryService, InquiryStatus, InquiryTypeEnum, JibitFailedResultType,
    ProviderType,
    ServiceResult
} from "@/lib/types";
import {ClientSession} from "mongoose";
import inquiryRepository from "@/features/inquiry/inquiry.repository";
import {InternalServerError} from "@/lib/error";
import {handleInquiryServiceError} from "@/utils/helpers-server";
import JibitFetchClient from "@/features/jibit/jibit.fetchClient";

type IbanInput = {
    iban: string
} & BaseParamsType

class IbanService implements IInquiryService<IbanInput, ServiceResult | undefined> {

    async lookup({
                     iban,
                     userId,
                     source,
                     validPeriod,
                     phone,
                     balance,
                     balance_after
                 }: IbanInput, session?: ClientSession | null): Promise<GenericResponse<any, undefined>> {

        const createBaseParams = {
            provider: ProviderType.JIBIT,
            iban,
            user: userId,
            source,
            phone,
            inquiryType: InquiryTypeEnum.Jibit_IbanInquiry,
            balance
        }

        try {
            const jibitFetchClient = await JibitFetchClient.create()
            let response: BankInquiryType | null = null;
            try {
                response = await jibitFetchClient.iban(iban);
            } catch (error: any) {
                const errorResult = error?.errors;
                await inquiryRepository.create<JibitFailedResultType>({
                    ...createBaseParams,
                    inquiryStatus: InquiryStatus.FAILED,
                    inquiryResult: errorResult,
                    paid: false
                }, session)
                return {
                    success: 'FAILED',
                    message: error?.message
                }
            }

            if (!response) {
                throw new InternalServerError();
            }

            const savedResult = await inquiryRepository.create<BankInquiryType>({
                ...createBaseParams,
                validPeriod,
                inquiryStatus: InquiryStatus.SUCCESS,
                inquiryResult: response as BankInquiryType,
                paid: true,
                balance_after,
            }, session)

            return {
                success: 'SUCCESS',
                data: {
                    id: savedResult!._id.toString(),
                    trackingNumber: savedResult!.trackingNumber!
                },
            };
        } catch (error: any) {
            console.error('خطا در jibit-conversion-query.service.ts', error);
            return handleInquiryServiceError(error);
        }
    }

}

export default IbanService