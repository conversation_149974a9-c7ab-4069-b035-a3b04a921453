import {<PERSON>, Card<PERSON>ontent, <PERSON>Footer, CardHeader} from "@/components/ui/card";
import CustomButton from "@/components/common/custom-button";
import {HOME_PATH} from "@/lib/routes";
import {GhabzinoBankType} from "@/lib/types";
import Image from "next/image";
import DataGhabzinoActionButtons from "@/components/result/data-ghabzino-action-buttons";
import {useMemo} from "react";
import {banks} from "@/lib/data/ghabzino-bank";

type Props = {
    result: GhabzinoBankType
    newInquiryurl?: string
}

export default function GhabzinoBankInquiry({result, newInquiryurl}: Props) {

    const bankName = useMemo(() => {
        if (!result?.bankShowName) {
            return banks.find(item => item.bank === result.bankName)?.bankNameFarsi
        }

    }, [result])

    return (

        <Card className="w-full !h-fit max-w-lg lg:max-w-2xl bg-white rounded-lg overflow-hidden">
            <CardHeader className="flex flex-row justify-between items-start pb-2 pt-6 px-6">
                <h2 className="text-2xl font-bold">نتیجه استعلام</h2>
                {result?.imageUrl && <div className="flex items-center">
                    <Image src={result?.imageUrl} width={50} height={50} alt={result?.bankName || "لوگوی بانک"}/>
                </div>}
            </CardHeader>

            <CardContent className="px-6" dir='rtl'>
                <div className="space-y-4">
                    {(result?.bankShowName || bankName) && <div className="flex gap-x-5 items-center">
                        <span className="">نام بانک:</span>
                        <span
                            className="text-gray-600 font-semibold">{result?.bankShowName || bankName}</span>
                    </div>}
                    {result?.depositNumber && <div className="flex gap-x-5 items-center">
                        <span className="">شماره حساب:</span>
                        <span
                            className="text-gray-600 font-semibold">{result?.depositNumber}</span>
                    </div>}
                    {result?.cardNumber && <div className="flex gap-x-5 items-center">
                        <span className="">شماره کارت:</span>
                        <span
                            className="text-gray-600 font-semibold">{result?.cardNumber}</span>
                    </div>}
                    {
                        Object.entries(result?.extraInfo || {}).map(([key, value], index) => (
                            <div key={index} className="flex gap-x-5 items-center">
                                <span className="">{key}:</span>
                                <span
                                    className="text-gray-600 font-semibold">{value}</span>
                            </div>
                        ))
                    }

                    {result?.shebaNumber && <div className="flex gap-x-5 items-center">
                        <span className="">شماره شبا:</span>
                        <span className="text-gray-600 font-semibold">{result?.shebaNumber}</span>
                    </div>
                    }
                    {result?.description &&
                        <div className="flex gap-x-5 flex-col justify-start font-semibold gap-y-1 items-start">
                            <span>{result?.description}</span>
                        </div>}
                </div>

                {!result?.description && <DataGhabzinoActionButtons result={result}/>}

            </CardContent>
            <div className="border-t border-gray-200 my-6"></div>
            <CardFooter className="px-6 pb-6">
                <CustomButton href={newInquiryurl || HOME_PATH} variants='green'>استعلام جدید</CustomButton>
            </CardFooter>
        </Card>
    )
}
