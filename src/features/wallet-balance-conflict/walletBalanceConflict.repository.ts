import WalletBalanceConflictModel from "@/features/wallet-balance-conflict/wallet-balance-conflict.schema";
import {ClientSession} from "mongoose";

class WalletBalanceConflictRepository {

    async create({phone, balanceInUser, balanceInWallet, userId, description}: {
        userId: string,
        phone: string,
        description: string,
        balanceInWallet: number,
        balanceInUser: number,
    }, session: ClientSession | null = null) {
        const newConflict = new WalletBalanceConflictModel({
            phone,
            user: userId,
            balance_in_wallet: balanceInWallet,
            balance_in_user: balanceInUser,
            description
        })

        return newConflict.save({session});
    }
}

export default new WalletBalanceConflictRepository();