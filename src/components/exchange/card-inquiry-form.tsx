'use client'

import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger} from "@/components/ui/tabs";
import {
    IranBankCardType,
} from "@/lib/zod-schemas";
import useInquiry from "@/lib/hooks/useInquiry";
import toast from "react-hot-toast";
import {usePathname, useRouter, useSearchParams} from "next/navigation";
import {InquiryTypeEnum, ServiceType} from "@/lib/types";
import {DynamicFormLoader} from "@/components/common/inquiry/dynamic-form-loader";
import {useMemo} from "react";
import {cn} from "@/lib/utils";

type Props = {
    services: ServiceType[]
}

export default function CardInquiryForm({services}: Props) {
    const {mutate, isLoading} = useInquiry();
    const router = useRouter();
    const searchParams = useSearchParams()
    const pathname = usePathname()
    const defaultValue = services.length > 0 ? services[0].key : undefined;
    const selected = searchParams.get("selected");

    const isSelectedValid = useMemo(() => {
        return services.some(service => service.key === selected)
    }, [services, selected]);

    async function onSubmit(
        values: any,
        type: InquiryTypeEnum) {

        const mutateResult = await mutate({
            ...values,
            inquiryType: type,
            inquiryPath: pathname,
            cardNumber: (values as IranBankCardType)?.cardNumber ? (values as IranBankCardType)?.cardNumber.join('') : undefined
        })

        if (!mutateResult.success && mutateResult.href) {
            router.push(mutateResult.href)
        } else if (!mutateResult.success && mutateResult.message) {
            toast.error(mutateResult.message);
        } else if (mutateResult.success && mutateResult.href) {
            router.push(mutateResult.href)
        }

    }

    return (
        <Tabs defaultValue={isSelectedValid ? selected! : defaultValue} className="w-full mt-4 ">
            {services.length > 1 && <TabsList
                className={cn(`grid w-full bg-transparent gap-1 mb-4 md:mb-8  h-fit rounded-lg`, {
                    'grid-cols-2': services.length === 2,
                    'grid-cols-3': services.length > 2
                })}>
                {
                    services.map((service, index) => (
                        <TabsTrigger
                            key={index}
                            value={service.key}
                            className="rounded-md text-xs md:text-sm bg-[#F9FAFB] text-neutral-600 hover:bg-slate-50
                            transition-all duration-100 h-[35px]
                             data-[state=active]:bg-blue-100 border data-[state=active]:border-blue-200
                             data-[state=active]:text-gray-800 md:h-[40px] py-3 cursor-pointer">
                            {service.title}
                        </TabsTrigger>
                    ))
                }
            </TabsList>}

            {
                services.map((service, index) => (
                    <TabsContent key={index} value={service.key} className="mt-0">
                        <DynamicFormLoader
                            componentKey={service.key}
                            isLoading={isLoading}
                            onSubmit={onSubmit}
                            price={service.price}
                        />
                    </TabsContent>
                ))
            }
        </Tabs>
    );
}
