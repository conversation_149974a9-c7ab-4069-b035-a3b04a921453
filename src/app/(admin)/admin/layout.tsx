import type React from "react"

import {<PERSON>barInset, SidebarProvider, SidebarTrigger} from "@/components/ui/sidebar"
import {AdminSidebar} from "@/components/dashboard/admin-sidebar";

export const dynamic = 'force-dynamic';
export default function AdminLayout({
                                        children,
                                    }: {
    children: React.ReactNode
}) {
    return (
        <SidebarProvider>
            <AdminSidebar/>
            <SidebarInset>
                <header className="flex h-16 shrink-0 items-center justify-between gap-2 border-b px-4">
                    <div className="flex items-center gap-2">
                        <SidebarTrigger className="-mr-1 cursor-pointer"/>
                    </div>
                </header>
                <main className="flex-1 py-0 md:py-14 md:px-3 bg-[#F8FAFC]">
                    <div className="bg-white  md:max-w-[1080px] md:mx-auto  md:rounded-lg md:shadow-2xl">
                        {children}
                    </div>
                </main>
            </SidebarInset>
        </SidebarProvider>
    )
}
