import Container from "@/components/common/container";
import InquiryWrapperComponent from "@/components/exchange/inquiryWrapperComponent";
import {CategorySlugEnum} from "@/lib/types";

// export const dynamic = 'force-dynamic';
export default async function ExchangePage() {
    return (
        <>
            <Container>
                <div className="w-full">
                    <InquiryWrapperComponent
                        categorySlug={CategorySlugEnum.BANKING_CONVERSION_INQUIRY}
                        description='استعلام تبدیل شماره کارت و حساب'
                        categoryTitle='پشتیبانی از تمام بانک ها'
                    />
                </div>
            </Container>
        </>
    );
}
