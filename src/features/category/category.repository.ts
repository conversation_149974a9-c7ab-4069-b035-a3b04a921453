import CategoryModel from "@/features/category/category.schema";
import {CategoryServiceUpsertInput, CategorySlugEnum, ICategory} from "@/lib/types";
import {SOURCE_NAME} from "@/lib/constants";
import {categoryList} from "@/lib/data/category-list";

class CategoryRepository {

    async seedCategories(): Promise<ICategory[]> {
        return CategoryModel.insertMany(categoryList);
    };


    async getCategories(active?: boolean): Promise<ICategory[]> {
        const filter: any = {source: SOURCE_NAME};

        if (typeof active === 'boolean') {
            filter.active = active;
        }

        return CategoryModel.find(filter);
    }

    async create({title, active, imagePath, slug}: CategoryServiceUpsertInput) {
        return CategoryModel.create({
            title,
            active,
            slug,
            image_url: imagePath,
        });
    }

    async findById(id: string): Promise<ICategory | null> {
        return CategoryModel.findById(id);
    }

    async findBySlug(slug: CategorySlugEnum): Promise<ICategory | null> {
        return CategoryModel.findOne({
            source: SOURCE_NAME,
            slug
        });
    }

    async update({id, title, imagePath, slug}: CategoryServiceUpsertInput) {
        return CategoryModel.findByIdAndUpdate(id, {
            title,
            slug,
            ...(imagePath && {image_url: imagePath}),
        });
    }
}

export default new CategoryRepository();