'use client'

import {InquiryTypeEnum, ServiceComponentProps} from "@/lib/types";
import {Form, FormControl, FormField, FormItem, FormMessage} from "@/components/ui/form";
import IranBankCardNumber from "@/components/common/bank-card-input";
import CustomButton from "@/components/common/custom-button";
import {useForm} from "react-hook-form";
import {iranBankCardArraySchema, IranBankCardType} from "@/lib/zod-schemas";
import {zodResolver} from "@hookform/resolvers/zod";
import InquiryPrice from "@/components/common/inquiry/inquiry-price";

export default function CardToIbanForm({isLoading, onSubmit, price}: ServiceComponentProps) {

    const formCardToIban = useForm<IranBankCardType>({
        resolver: zodResolver(iranBankCardArraySchema),
        mode: 'onSubmit',
        defaultValues: {
            cardNumber: ["", "", "", ""]
        },
    })

    async function handleSubmit(values: any) {
        if (isLoading) return;
        onSubmit?.(values, InquiryTypeEnum.Jibit_CardToIban)
    }

    return (
        <>
            <Form {...formCardToIban}>
                <form
                    onSubmit={formCardToIban.handleSubmit((values) => handleSubmit(values))}
                    className="space-y-6">
                    <FormField
                        control={formCardToIban.control}
                        name="cardNumber"
                        render={({field}) => (
                            <FormItem className='w-full'>
                                <p className='font-light mb-7 text-neutral-700 text-sm md:text-base'>شماره
                                    کارت را
                                    وارد کنید، سپس دکمه
                                    تبدیل را
                                    انتخاب کنید
                                </p>
                                <FormControl>
                                    <IranBankCardNumber
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage className='text-sm'/>
                            </FormItem>
                        )}
                    />
                    <CustomButton
                        loading={isLoading}
                        disabled={isLoading}
                        variants='blue'
                        className="mt-1"
                        type="submit">تبدیل به شبا
                    </CustomButton>
                </form>
            </Form>
            <InquiryPrice price={price}/>
        </>
    );
}
