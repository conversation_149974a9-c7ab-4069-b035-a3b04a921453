'use client'

import {formatWithComma} from "@/utils/helpers";
import CustomButton from "@/components/common/custom-button";
import {CreditCard, Minus, Plus} from "lucide-react";
import {ChangeEvent, useEffect, useState} from "react";
import {useRouter} from "next/navigation";
import {cn} from "@/lib/utils";
import {InquiryQueryParams} from "@/lib/types";
import usePathUrlHelper from "@/lib/hooks/usePathUrlHelper";
import {paymentRequest} from "@/features/transaction/transaction.action";
import {PAYMENT_RESULT_PATH} from "@/lib/routes";
import toast from "react-hot-toast";

const MIN_AMOUNT = 100_000
const MAX_AMOUNT = 1000_000
const predefinedAmounts = [100000, 200000]

export default function DepoComponent() {
    const [amount, setAmount] = useState(predefinedAmounts[1]);
    const [errorMessage, setErrorMessage] = useState('')
    const [isLoading, setIsLoading] = useState(false)
    const [increaseBtnDisabled, setIncreaseBtnDisabled] = useState(false)
    const [decreaseBtnDisabled, setDecreaseBtnDisabled] = useState(false)
    const [inquiryInfo, setInquiryInfo] = useState<InquiryQueryParams | undefined>();
    const {getQueryParams} = usePathUrlHelper();
    const router = useRouter()
    const message = `مبلغ وارد شده باید بین ${formatWithComma(MIN_AMOUNT.toString())}  تا ${formatWithComma(MAX_AMOUNT.toString())} تومان باشد `


    const handleAmountChange = (value: number) => {
        if (value < MIN_AMOUNT || value > MAX_AMOUNT) {
            setErrorMessage(message);
        } else {
            setErrorMessage("")
        }
        setAmount(value);
    };

    useEffect(() => {
        setDecreaseBtnDisabled(false)
        setIncreaseBtnDisabled(false)
        if (amount <= MIN_AMOUNT) {
            setDecreaseBtnDisabled(true)
        } else if (amount >= MAX_AMOUNT) {
            setIncreaseBtnDisabled(true)
        }
    }, [amount]);

    useEffect(() => {
        const params = getQueryParams<InquiryQueryParams>();
        if (params?.inquiryType) {
            setInquiryInfo(params)
        }
    }, []);


    const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
        const rawValue = e.currentTarget.value.replace(/,/g, ""); // Remove commas
        const numericValue = Number(rawValue);
        if (numericValue < MIN_AMOUNT || numericValue > MAX_AMOUNT) {
            setErrorMessage(message);
        } else {
            setErrorMessage("")
        }
        if (!isNaN(numericValue)) {
            setAmount(numericValue);
        }
    };

    async function onPaymentButtonClick() {
        if (isLoading) return;

        setIsLoading(true)

        if (!amount) {
            setErrorMessage(message)
            setIsLoading(false)
            return;
        }
        if (amount < MIN_AMOUNT) {
            setErrorMessage(message)
            setIsLoading(false)
            return;
        }
        if (amount > MAX_AMOUNT) {
            setErrorMessage(message)
            setIsLoading(false)
            return;
        }

        const actionResult = await paymentRequest({
            amount,
            callbackUrl: `${window.location.origin}${PAYMENT_RESULT_PATH}`,
            inquiry: inquiryInfo || undefined,
        })

        if (!actionResult.success) {
            toast.error(actionResult.message!)
            setIsLoading(false)
            return;
        }
        router.push(actionResult.data!.payment_url)
    }


    return (
        <div className="w-full md:min-w-xl h-fit rounded-xl bg-white px-6 md:px-14 py-10 shadow-lg">
            {inquiryInfo &&
                <h3 className='text-destructive text-base text-center md:text-lg mb-4'>موجودی حساب شما کافی نیست, لطفا
                    ابتدا کیف پول
                    خود را شارژ
                    کنید
                </h3>}
            <div className="mb-8 text-center">
                <p className="text-gray-700">مبلغ را انتخاب کنید</p>
            </div>
            <div className="mb-8 space-y-3">
                <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
                    {
                        predefinedAmounts.map((price, index) => (
                            <button
                                key={index}
                                onClick={() => handleAmountChange(price)}
                                className={cn('flex h-14 cursor-pointer w-full items-center justify-center rounded-lg border-2 bg-blue-50 px-4 py-3 text-blue-600 transition hover:bg-blue-100', {
                                    'border-green-600': amount === price,
                                })}>
                                <span className="text-lg">{formatWithComma(price.toString())}</span>
                                <span className='mx-1'>تومان</span>
                            </button>
                        ))
                    }
                </div>
                <div
                    className={`flex mt-8 items-center border ${errorMessage ? "border-red-500" : "border-gray-300"} rounded-2xl px-5 py-3 w-full`}>
                    <button
                        className="bg-[#C5CBD4] disabled:bg-[#C5CBD4]/50 cursor-pointer disabled:cursor-auto rounded-full flex items-center justify-center w-10 h-8"
                        onClick={() => handleAmountChange(amount + 10000)}
                        disabled={increaseBtnDisabled}
                    >
                        <Plus size={16} color="white"/>
                    </button>
                    <input
                        type="tel"
                        dir="rtl"
                        className="left-direction text-center w-full bg-transparent text-lg font-semibold focus:outline-none"
                        value={amount.toLocaleString()}
                        maxLength={8}
                        onChange={handleInputChange}
                    />

                    <button
                        className=" bg-[#C5CBD4] disabled:bg-[#C5CBD4]/50 cursor-pointer disabled:cursor-auto rounded-full flex items-center justify-center w-10 h-8"
                        onClick={() => handleAmountChange(amount - 10000)}
                        disabled={decreaseBtnDisabled}
                    >
                        <Minus size={16} color="white"/>
                    </button>
                </div>
                {errorMessage && <p className="text-red-500 text-center text-sm mt-2">{errorMessage}</p>}
            </div>

            <CustomButton
                loading={isLoading}
                disabled={isLoading}
                variants='green'
                onClick={onPaymentButtonClick}>
                <CreditCard className="h-5 w-5"/>
                <span className="mx-2 text-lg">پرداخت</span>
            </CustomButton>
        </div>
    );
}
