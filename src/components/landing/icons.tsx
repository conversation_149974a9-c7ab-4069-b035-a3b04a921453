import {Building, <PERSON><PERSON>heck, Car, ArrowRightLeft} from "lucide-react";
import {CategorySlugEnum} from "@/lib/types";
import {JSX} from "react";

export const iconMap: Record<CategorySlugEnum, { renderIcon: () => JSX.Element, className: string }> = {
    [CategorySlugEnum.BANKING_INQUIRY]: {
        renderIcon: () => <Building className="w-7 h-7 md:w-10 md:h-10 text-white"/>,
        className: 'bg-orange-400'
    },
    [CategorySlugEnum.SAYADI_CHECK_INQUIRY]: {
        renderIcon: () => <FileCheck className="w-7 h-7 md:w-10 md:h-10 text-white"/>,
        className: 'bg-blue-500'
    },
    [CategorySlugEnum.KHALAFI_INQUIRY]: {
        renderIcon: () => <Car className="w-7 h-7 md:w-10 md:h-10 text-white"/>,
        className: 'bg-green-500'
    },
    [CategorySlugEnum.BANKING_CONVERSION_INQUIRY]: {
        renderIcon: () => <ArrowRightLeft className="w-10 h-10 text-white"/>,
        className: 'bg-orange-500'
    }
}

