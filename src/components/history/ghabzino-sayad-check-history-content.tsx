import {Card, CardContent} from "@/components/ui/card";
import {GhabzinoResultType} from "@/lib/types";
import {miladiToShamsi} from "@/utils/helpers";

type Props = {
    inquiry: GhabzinoResultType
}

export default function GhabzinoSayadCheckHistoryContent({inquiry}: Props) {
    return (
        <div className="mb-4">
            <Card className="overflow-hidden py-0 gap-y-4">
                <div className="flex px-4 py-2 bg-neutral-100 items-center justify-between">
                    <h3 className="text-base font-semibold text-neutral-500">استعلام چک صیادی</h3>
                </div>
                <CardContent className=" px-2 pb-4">
                    <div className="grid gap-2 text-right">
                        <div className="grid gap-2 px-2">
                            {inquiry.sayadiResult?.OwnerName &&
                                <div className="flex items-center justify-between">
                                                        <span
                                                            className="font-medium text-base">نام صادر شده:</span>
                                    <div className='flex items-center gap-x-1'>
                                        <span>{inquiry.sayadiResult?.OwnerName}</span>
                                    </div>
                                </div>}
                            {(inquiry.sayadiResult?.SayadID || inquiry?.sayadId) &&
                                <div className="flex justify-between">
                                    <span className="font-medium">شناسه صیادی:</span>
                                    <span>{inquiry?.sayadiResult?.SayadID || inquiry?.sayadId}</span>
                                </div>}
                            {inquiry.sayadiResult?.Date && <div className="flex justify-between">
                                <span className="font-medium">تاریخ چک:</span>
                                <span dir="ltr" className="text-left">
                                                       {miladiToShamsi(inquiry.sayadiResult?.Date)}
                                                    </span>
                            </div>
                            }
                            {inquiry.sayadiResult?.Color && <div className="flex justify-between">
                                <span className="font-medium">رنگ چک:</span>
                                <span dir="ltr" className="text-left">
                                                        {inquiry.sayadiResult?.Color}
                                                    </span>
                            </div>
                            }
                            <div
                                className="flex gap-x-5 flex-col justify-start font-semibold gap-y-1 items-start">
                                <span>{inquiry.sayadiResult?.Description || inquiry?.description}</span>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}
