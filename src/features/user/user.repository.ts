import {headers} from "next/headers"
import userModel from "@/features/user/user.schema";
import {IUser} from "@/lib/types";
import {ClientSession} from "mongoose";

class UserRepository {

    async getUserAgent() {
        const headersList = await headers()
        const userAgent = headersList.get('user-agent') || "Unknown userAgent"
        const ip = headersList.get("x-forwarded-for") || "Unknown IP"
        return {ip, userAgent};
    }

    async findUserByPhone(
        {phone, source}: { phone: string, source: string },
        session: ClientSession | null = null
    ): Promise<IUser | null> {
        return userModel.findOne({
            phone,
            source,
        }).session(session);
    };

    async createUser({phone, source}: {
        phone: string,
        source: string
    }, session: ClientSession | null = null): Promise<IUser> {

        const user = new userModel({
            phone,
            source,
        });
        return await user.save({session});
    };

    async adjustBalance({userId, amount, source}: {
        userId: string,
        amount: number
        source: string
    }, session: ClientSession | null = null): Promise<number | null> {

        const update = {$inc: {balance: amount}}

        const user = await userModel.findOneAndUpdate<IUser | null>({
                _id: userId,
                ...(amount < 0 ? {balance: {$gte: -amount}} : {}),
                source,
            },
            update,
            {
                session,
                new: true
            }
        )

        if (user !== null) {
            return user.balance
        }
        return null;
    }

    // deductBalanceForInquiry(userId: string) {
    //     const env = envConfig()
    //     return userModel.findOneAndUpdate(
    //         {_id: userId, balance: {$gte: env.INQUIRY_PRICE}},
    //         {$inc: {balance: -env.INQUIRY_PRICE}},
    //         {new: true}
    //     );
    // }
    //
    // refundBalanceForInquiry(userId: string) {
    //     const env = envConfig()
    //     return userModel.findByIdAndUpdate(userId, {
    //         $inc: {balance: env.INQUIRY_PRICE},
    //     });
    // }

    async findUserById({id, source}: {
        id: string,
        source: string
    }, session: ClientSession | null = null): Promise<IUser | null> {

        return userModel.findOne({
            _id: id,
            source,
        }).session(session)
    }

    // chargeWallet(arg: ApiService) {
    //     return fetchApi.post("user/charge-depo", arg?.payload || {})
    // }

}

export default new UserRepository()
