'use server'

import {
    ActionResult,
    PaymentRequestInput,
    PaymentResultResponse,
    PaymentStatus,
    TransactionType,
    VerifyPaymentResponse,
} from "@/lib/types";
import {connectToDatabase} from "@/lib/mongodb";
import {
    getAppSource,
    getCurrentUser,
    handleActionErrorResponse, handleBalanceOperation, handleInquiryValidation,
    handleZodValidationResult
} from "@/utils/helpers-server";
import {BadRequestError, NotFoundError, UnauthorizedError} from "@/lib/error";

import {paymentRequestSchema} from "@/lib/zod-schemas";
import zarinpalService from "@/lib/services/gateways/zarinpal/zarinpal.service";
import mongoose from "mongoose";
import userRepository from "@/features/user/user.repository";
import transactionRepository from "@/features/transaction/transaction.repository";
import cookieService from "@/lib/services/cookie.service";
import ConversionService from "@/lib/services/conversion.service";
import applicationRepository from "@/features/application/application.repository";


export async function paymentRequest({
                                         callbackUrl,
                                         amount,
                                         inquiry,
                                         description
                                     }: PaymentRequestInput): Promise<ActionResult<PaymentResultResponse>> {
    try {
        await connectToDatabase()
        const currentUser = await getCurrentUser();

        if (!currentUser) {
            throw new UnauthorizedError()
        }
        const source = await getAppSource()

        handleZodValidationResult(paymentRequestSchema.safeParse({amount, callbackUrl}));

        if (inquiry) {
            handleInquiryValidation(inquiry)
        }


        const zarinpalResult = await zarinpalService.paymentRequest({callbackUrl, amount, description});

        if (zarinpalResult.success === 'FAILED') {
            throw new BadRequestError("درخواست پرداخت ناموفق بود")
        }

        const {ip, userAgent} = await userRepository.getUserAgent()

        await transactionRepository.createTransaction({
            amount,
            phone: currentUser.phone!,
            source,
            inquiry,
            agent: userAgent,
            ip,
            authority: zarinpalResult.data!.authority!,
        })

        return {
            success: true,
            data: {
                payment_url: zarinpalResult.data!.url,
            }
        }

    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}

export async function verifyPayment(authority: string, status: string): Promise<ActionResult<VerifyPaymentResponse>> {
    await connectToDatabase()
    const session = await mongoose.startSession();
    session.startTransaction();
    try {
        const currentUser = await getCurrentUser();

        if (!currentUser) {
            throw new UnauthorizedError()
        }

        if (!authority) {
            const errorMessage = 'شماره مرجع (Authority) موجود نیست.'
            await session.commitTransaction();
            return {
                success: false,
                message: errorMessage
            }
        }

        const source = await getAppSource()

        const userFromDb = await userRepository.findUserById({source, id: currentUser.sub!}, session);

        if (!userFromDb) {
            throw new NotFoundError('کاربر یافت نشد!')
        }

        const userId = userFromDb._id.toString()
        const phone = userFromDb.phone!;

        const transactionFromDb = await transactionRepository.findTransactionByAuthorityAndPhone({
            authority,
            phone,
            source
        }, session);

        if (!transactionFromDb) {
            throw new NotFoundError("تراکنشی با شماره مرجع (Authority) برای کاربر ثبت نشده")
        }

        if (transactionFromDb.status !== PaymentStatus.PENDING) {
            await session.commitTransaction();
            return {
                success: false,
                message: 'این تراکنش قبلا پردازش شده.'
            }
        }

        if (status !== 'OK') {
            transactionFromDb.status = PaymentStatus.FAILED;
            await transactionFromDb.save({session})
            await session.commitTransaction();
            return {
                success: false,
                message: 'عملیات پرداخت ناموفق بود. در صورت تمایل می‌توانید مجدداً اقدام کنید.'
            }
        }


        const zarinpalResult =
            await zarinpalService.verifyPayment({amount: transactionFromDb.amount, authority});

        if (zarinpalResult.success === 'FAILED') {
            transactionFromDb.status = PaymentStatus.FAILED;
            transactionFromDb.code = zarinpalResult.errors?.code.toString()
            await transactionFromDb.save({session})
            await session.commitTransaction();
            return {
                success: false,
                message: 'عملیات پرداخت ناموفق بود. در صورت تمایل می‌توانید مجدداً اقدام کنید.',
            }
        }

        transactionFromDb.status = PaymentStatus.SUCCESS;
        transactionFromDb.cardNumber = zarinpalResult.data!.card_pan;
        transactionFromDb.code = zarinpalResult.data?.code.toString();
        transactionFromDb.refId = zarinpalResult.data?.ref_id.toString()
        transactionFromDb.result_payment = {
            RefID: zarinpalResult.data!.ref_id.toString(),
            Status: zarinpalResult.data!.code
        }
        await transactionFromDb.save({session})

        const [gclId, {ip, userAgent}, applicationFromDb] = await Promise.all([
            cookieService.getgclid(),
            userRepository.getUserAgent(),
            applicationRepository.getByHost(source, session),
            handleBalanceOperation({
                userId,
                source,
                phone,
                amount: transactionFromDb.amount,
                referenceId: transactionFromDb._id.toString(),
                referenceType: TransactionType.DEPOSIT,
                reason: 'بابت پرداخت از درگاه بانکی'
            }, session)
        ])
        const shouldConvert = !!(applicationFromDb?.ga_apiSecret && applicationFromDb?.ga_measurementId)
        if (shouldConvert) {
            ConversionService.sendPurchaseConversion({
                source,
                agent: userAgent,
                ip,
                clientId: phone,
                gclid: gclId || undefined,
                mobile: phone,
                value: transactionFromDb.amount,
                transactionId: transactionFromDb._id.toString()
            })
        }

        await session.commitTransaction();
        return {
            success: true,
            data: {
                amount: transactionFromDb.amount,
                ref_id: zarinpalResult.data!.ref_id,
                hasInquiry: !!transactionFromDb.inquiry?.inquiryType,
                inquiry: transactionFromDb.inquiry
            },
            message: 'پرداخت با موفقیت انجام شد و مبلغ به کیف پول شما افزوده شد.'
        }

    } catch (error: any) {
        await session.abortTransaction();
        return handleActionErrorResponse(error)
    } finally {
        await session.endSession();
    }
}