'use client'

import {useAuth} from "@/lib/hooks/useAuth";
import Link from "next/link";
import {LOGIN_PATH} from "@/lib/routes";
import {User} from "lucide-react";
import UserLoggedInButton from "@/components/header/UserLoggedInButton";

export default function DisplayAuthStatus() {
    const {userData, logoutUser} = useAuth()
    return (
        <>
            {userData === null && <Link href={LOGIN_PATH} className="flex items-center gap-2 cursor-pointer">
                <User size={25}/>
                <span className='text-base md:text-xl'>ورود / عضویت</span>
            </Link>
            }
            {
                userData && <UserLoggedInButton logOutUser={logoutUser} user={userData}/>
            }
        </>
    );
}
