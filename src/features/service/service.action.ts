'use server'

import {ActionResult, InquiryTypeEnum, IService, ServiceType, SettingsValueTypeEnum} from "@/lib/types";
import {connectToDatabase} from "@/lib/mongodb";
import {getCurrentUser, handleActionErrorResponse} from "@/utils/helpers-server";
import {ConflictError, ForbiddenError, NotFoundError, UnauthorizedError} from "@/lib/error";
import serviceRepository from "@/features/service/service.repository";
import categoryRepository from "@/features/category/category.repository";


export async function getOrSeedAppServices(): Promise<ActionResult<Partial<ServiceType>[]>> {
    try {
        await connectToDatabase()

        let servicesFromDb: IService[] = [];
        servicesFromDb = await serviceRepository.getServices();
        if (servicesFromDb.length === 0) {
            servicesFromDb = await serviceRepository.seedServices();
        }

        return {
            success: true,
            data: servicesFromDb.map((item) => ({
                key: item.key as InquiryTypeEnum,
                value: item.value,
                category: item.category,
                title: item.title,
                price: item.price.toString(),
                provider: item.provider,
                validPeriod: item.validPeriod,
                inquiryPolicy: item.inquiryPolicy,
                version: item.__v
            })),
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}

export async function createService(service: ServiceType): Promise<ActionResult<boolean>> {
    try {
        await connectToDatabase()

        const newService = await serviceRepository.create(service)

        return {
            success: true,
            data: !!newService,
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}

export async function seedServices() {
    try {
        await connectToDatabase()

        let servicesFromDb = await serviceRepository.getServices();

        if (servicesFromDb.length === 0) {
            servicesFromDb = await serviceRepository.seedServices();
        }

        return {
            success: true,
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}

export async function toggleServiceActive(key: InquiryTypeEnum): Promise<ActionResult> {
    try {
        await connectToDatabase()

        const currentUser = await getCurrentUser();

        if (!currentUser) {
            throw new UnauthorizedError()
        }

        if (!currentUser?.admin) {
            throw new ForbiddenError()
        }

        const serviceFromDb = await serviceRepository.findByKey(key);

        if (!serviceFromDb) {
            throw new NotFoundError(`سرویس با key ${key} یافت نشد`)
        }

        serviceFromDb.value =
            serviceFromDb.value === SettingsValueTypeEnum.ACTIVE ? SettingsValueTypeEnum.DEACTIVE : SettingsValueTypeEnum.ACTIVE

        await serviceFromDb.save()

        const [activeServices, category] = await Promise.all(
            [serviceRepository.getServicesByCategory(serviceFromDb.category)
                , categoryRepository.findBySlug(serviceFromDb.category)]
        );

        if (category) {
            const shouldBeActive = activeServices.length > 0;
            if (category.active !== shouldBeActive) {
                category.active = shouldBeActive;
                await category.save();
            }
        }

        return {
            success: true,
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}

export async function updateServiceByKey(key: InquiryTypeEnum, {title, price, expectedVersion, validPeriod}: {
    title: string,
    price: number,
    validPeriod: number,
    category?: string,
    expectedVersion: number
}): Promise<ActionResult> {
    try {
        await connectToDatabase()

        const currentUser = await getCurrentUser();

        if (!currentUser) {
            throw new UnauthorizedError()
        }

        if (!currentUser?.admin) {
            throw new ForbiddenError()
        }

        const serviceFromDb = await serviceRepository.findByKey(key);

        if (!serviceFromDb) {
            throw new NotFoundError(`سرویس با key ${key} یافت نشد`)
        }

        if (serviceFromDb.__v !== expectedVersion) {
            throw new ConflictError('این سرویس قبلا توسط شخص دیگری ویرایش شده. لطفا صفحه را رفرش کنید تا آخرین نسخه بارگزاری شود')
        }

        serviceFromDb.title = title

        serviceFromDb.price = price

        serviceFromDb.validPeriod = validPeriod;

        await serviceFromDb.save()

        return {
            success: true,
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}