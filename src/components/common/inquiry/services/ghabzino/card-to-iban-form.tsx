'use client'

import {AccountTypeEnum, InquiryTypeEnum, ServiceComponentProps} from "@/lib/types";
import {Form, FormControl, FormField, FormItem, FormMessage} from "@/components/ui/form";
import IranBankCardNumber from "@/components/common/bank-card-input";
import CustomButton from "@/components/common/custom-button";
import {useForm} from "react-hook-form";
import {
    ghabzinoCardToIbanFormSchema,
    GhabzinoCardToIbanType,
} from "@/lib/zod-schemas";
import {zodResolver} from "@hookform/resolvers/zod";
import InquiryPrice from "@/components/common/inquiry/inquiry-price";
import {Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select";

export default function CardToIbanForm({isLoading, onSubmit, price}: ServiceComponentProps) {

    const formCardToIban = useForm<GhabzinoCardToIbanType>({
        resolver: zodResolver(ghabzinoCardToIbanFormSchema),
        mode: 'onSubmit',
        defaultValues: {
            cardNumber: ["", "", "", ""],
            accountTypeName: AccountTypeEnum.Deposit
        },
    })

    async function handleSubmit(values: any) {
        if (isLoading) return;
        onSubmit?.(values, InquiryTypeEnum.Ghanzino_CardToIban)
    }

    return (
        <>
            <Form {...formCardToIban}>
                <form
                    onSubmit={formCardToIban.handleSubmit((values) => handleSubmit(values))}
                    className="space-y-6">
                    <FormField
                        control={formCardToIban.control}
                        name="cardNumber"
                        render={({field}) => (
                            <FormItem className='w-full'>
                                <label className='text-neutral-600 text-sm'>شماره کارت</label>
                                <FormControl>
                                    <IranBankCardNumber
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage className='text-sm'/>
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={formCardToIban.control}
                        name="accountTypeName"
                        render={({field}) => (
                            <FormItem className='w-full'>
                                <label className='text-neutral-600 text-sm'>نوع حساب</label>
                                <FormControl>
                                    <Select
                                        {...field}
                                        value={field.value}
                                        onValueChange={(value) => field.onChange(value)}
                                    >
                                        <SelectTrigger className="w-full cursor-pointer h-[45px] md:!h-[60px]">
                                            <SelectValue className='text-neutral-400'
                                                         placeholder=""/>
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectGroup>
                                                <SelectItem
                                                    key={1}
                                                    value={AccountTypeEnum.Deposit}>سپرده</SelectItem>
                                                <SelectItem
                                                    key={2}
                                                    value={AccountTypeEnum.Loan}>تسهیلات</SelectItem>
                                            </SelectGroup>
                                        </SelectContent>
                                    </Select>
                                </FormControl>
                                <FormMessage className='text-sm'/>
                            </FormItem>
                        )}
                    />
                    <CustomButton
                        loading={isLoading}
                        disabled={isLoading}
                        variants='blue'
                        className="mt-1"
                        type="submit">تبدیل به شبا
                    </CustomButton>
                </form>
            </Form>
            <InquiryPrice price={price}/>
        </>
    );
}
