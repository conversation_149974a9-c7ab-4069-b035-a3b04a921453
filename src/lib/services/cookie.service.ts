import {cookies} from "next/headers";
import {AUTHORIZATION, GCL<PERSON>} from "@/lib/constants";
import envConfig from "@/lib/config.env";

class CookieService {


    static async setAuthorizationToken(token: string, expireAt: Date) {
        const env = envConfig();
        (await cookies()).set(AUTHORIZATION, token, {
            httpOnly: true,
            secure: env.NODE_ENV === 'production',
            sameSite: 'lax',
            expires: expireAt,
            path: "/"
        });
    }

    static async getgclid() {
        const cookieStore = await cookies();
        const gclidCookie = cookieStore.get(GCLID)
        return gclidCookie ? gclidCookie.value : null;
    }

    static async getAuthorizationToken() {

        const cookieStore = await cookies();
        const token = cookieStore.get(AUTHORIZATION); // Replace with your cookie name

        return token ? token.value : null;
    }

    static async deleteAuthorizationToken() {
        const cookieStore = await cookies();
        if (cookieStore.has(AUTHORIZATION)) {
            cookieStore.delete(AUTHORIZATION);
        }
    }
}

export default CookieService
