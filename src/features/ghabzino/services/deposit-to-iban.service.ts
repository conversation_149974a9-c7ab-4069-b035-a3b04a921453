import {
    BaseParamsType,
    GenericResponse, GhabzinoBankResult,
    GhabzinoResponse,
    IInquiryService, InquiryStatus, InquiryTypeEnum, ProviderType,
    ServiceResult
} from "@/lib/types";
import inquiryRepository from "@/features/inquiry/inquiry.repository";
import {InternalServerError} from "@/lib/error";
import {handleInquiryServiceError} from "@/utils/helpers-server";
import {ClientSession} from "mongoose";
import GhabzinoFetchclient from "@/features/ghabzino/ghabzino.fetchClient";

type DepositToIbanInquiryInput = {
    depositNumber: string,
    bank: string,
    accountTypeName: string,
} & BaseParamsType

class DepositToIbanService implements IInquiryService<DepositToIbanInquiryInput, ServiceResult | undefined> {

    async lookup({
                     accountTypeName,
                     depositNumber,
                     bank,
                     validPeriod,
                     userId,
                     phone,
                     source,
                     balance,
                     balance_after
                 }: DepositToIbanInquiryInput, session: ClientSession | null = null): Promise<GenericResponse<ServiceResult | undefined>> {
        try {

            const depositToIbanFromDb = await inquiryRepository.findOneGhabzinoDepositToIban({
                bank,
                accountTypeName: accountTypeName!,
                depositNumber: depositNumber!,
                requireValidExpireTime: false
            }, session);

            const createBaseParams: any = {
                accountTypeName,
                depositNumber,
                bank,
                source,
                user: userId,
                provider: ProviderType.GHABZINO,
                phone,
                balance,
                inquiryType: InquiryTypeEnum.Ghanzino_DepositToIban,
            }

            let response: GhabzinoResponse<GhabzinoBankResult> | undefined;
            let success = !!depositToIbanFromDb
            if (!depositToIbanFromDb) {
                try {
                    response = await GhabzinoFetchclient.depositToIbanInquiry({
                        bank,
                        depositNumber,
                        accountTypeName
                    })
                } catch (error: any) {
                    await inquiryRepository.create<GhabzinoResponse<GhabzinoBankResult>>({
                        ...createBaseParams,
                        inquiryStatus: InquiryStatus.FAILED,
                        inquiryResult: error?.message ? error.message : error,
                    }, session);

                    return {
                        success: "FAILED",
                        message: 'استعلام شما ناموفق بود لطفا مجددا تلاش کنید'
                    }
                }

                const successCodes = ['G00000', 'GD2251', 'GD2652', 'GD2255', 'GD2259'];
                success = successCodes.includes(response?.Status?.Code);
            }


            const savedResult = await inquiryRepository.create<GhabzinoResponse<GhabzinoBankResult>>({
                ...createBaseParams,
                inquiryStatus: success ? InquiryStatus.SUCCESS : InquiryStatus.FAILED,
                validPeriod: success ? validPeriod : undefined,
                inquiryResult: depositToIbanFromDb ? depositToIbanFromDb.inquiryResult : response,
                paid: success,
                balance_after: success ? balance_after : undefined,
            }, session);

            if (!savedResult) {
                throw new InternalServerError();
            }

            if (success) {
                return {
                    success: 'SUCCESS',
                    data: {
                        id: savedResult._id.toString(),
                        trackingNumber: savedResult.trackingNumber!,
                    }
                };
            }

            return {
                success: 'FAILED',
                message: response?.Status?.Description || 'خطای ناشناخته‌ای رخ داده است',
            };

        } catch (error: any) {
            return handleInquiryServiceError(error);
        }
    }
}

export default DepositToIbanService;