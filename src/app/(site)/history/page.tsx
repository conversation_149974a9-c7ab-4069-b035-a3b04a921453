import InquiryHistory from "@/components/history/inquiry-history";
import Container from "@/components/common/container";
import NoInquiryHistory from "@/components/history/no-inquiry-history";
import {redirect} from "next/navigation";
import {ERROR_PATH} from "@/lib/routes";
import {getInquiriesForHistory} from "@/features/inquiry/inquiry.action";

export const dynamic = 'force-dynamic';

export default async function HistoryPage() {
    const actionResult = await getInquiriesForHistory();
    if (!actionResult.success) {
        redirect(ERROR_PATH)
    }

    return (
        <Container>
            {(!actionResult.data || (actionResult.data && actionResult.data.length === 0)) && <NoInquiryHistory/>}
            {(actionResult.data && actionResult.data.length > 0) && <InquiryHistory inquiries={actionResult.data}/>}
        </Container>
    );
}
