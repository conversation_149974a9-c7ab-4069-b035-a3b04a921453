import LandingComponent from "@/components/landing/landing-component";
import Container from "@/components/common/container";
import {getOrSeedAppCategories} from "@/features/category/category.action";
import {redirect} from "next/navigation";
import {ERROR_PATH} from "@/lib/routes";

export const dynamic = 'force-dynamic';
export default async function LandingPage() {

    const actionResult = await getOrSeedAppCategories(true)

    if (!actionResult.success) {
        console.error(actionResult);
        redirect(ERROR_PATH)
    }

    return (
        <Container>
            <LandingComponent categories={actionResult.data!}/>
        </Container>
    );
}
