'use client'

import {useEffect} from 'react';
import {useSearchParams} from 'next/navigation';
import {setCookie} from 'cookies-next';
import {GCLID} from "@/lib/constants";

export default function GclidHandler() {
    const searchParams = useSearchParams();

    useEffect(() => {
        const gclid = searchParams.get(GCLID);
        if (gclid) {
            // 90-day expiration (in seconds)
            const maxAge = 90 * 24 * 60 * 60;
            setCookie(GCLID, gclid, {maxAge, path: '/'});
        }
    }, [searchParams]);

    return null;
};

