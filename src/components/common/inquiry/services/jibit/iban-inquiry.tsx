'use client'

import {InquiryTypeEnum, ServiceComponentProps} from "@/lib/types";
import IbanForm from "@/components/common/inquiry/iban-form";

export default function IbanInquiry({isLoading, onSubmit, price}: ServiceComponentProps) {

    return <IbanForm
        onSubmit={onSubmit}
        price={price}
        isLoading={isLoading}
        btnText='استعلام'
        inquiryType={InquiryTypeEnum.Jibit_IbanInquiry}/>
}
