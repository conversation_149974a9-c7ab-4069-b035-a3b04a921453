import {
    BaseParamsType,
    GenericResponse, GhabzinoResponse, GhabzinoTrafficInquiryParams, GhabzinoTrafficInquiryResult,
    GhabzinoTrafficInquiryServiceParams, IInquiryResult,
    IInquiryService, InquiryStatus, ProviderType,
    ServiceResult, TrafficFinesFetchParams
} from "@/lib/types";
import {ClientSession} from "mongoose";
import {convertPlaquePartsToString, handleInquiryServiceError} from "@/utils/helpers-server";
import inquiryRepository from "@/features/inquiry/inquiry.repository";
import tokenService from "@/lib/services/token.service";
import {HttpError, InternalServerError} from "@/lib/error";
import status from "http-status";
import GhabzinoFetchClient from "@/features/ghabzino/ghabzino.fetchClient";

class TrafficFinesService implements IInquiryService<GhabzinoTrafficInquiryServiceParams & BaseParamsType, ServiceResult | undefined> {
    async lookup(params: GhabzinoTrafficInquiryServiceParams & BaseParamsType,
                 session: ClientSession | null = null): Promise<GenericResponse<ServiceResult | undefined>> {
        try {
            const {
                Left,
                phone,
                userId,
                Mid,
                Alphabet,
                source,
                Right,
                NationalID,
                validPeriod,
                MobileNumber,
                withDetails,
                isMotor,
                inquiryType,
                balance,
                balance_after
            } = params;

            const plaqueText = convertPlaquePartsToString({
                left: Left,
                alphabet: Alphabet,
                middle: Mid,
                right: Right,
            })

            const pendingResultFromDb = await inquiryRepository.findOneGhabzinoTrafficFineInquiry({
                inquiryType,
                source,
                plaque: plaqueText,
                nationalID: NationalID || null,
                phoneNumber: MobileNumber || null,
                inquiryStatus: InquiryStatus.PENDING,
                requireValidExpireTime: true,
                phone,
                withDetails,
            }, session)

            const isPending = !!pendingResultFromDb
            const traceNumber = pendingResultFromDb?.inquiryParams?.TraceNumber ?? tokenService.generateUUID();

            const inquiryParams: GhabzinoTrafficInquiryParams = {
                Left,
                Mid,
                inquiryType,
                NationalID: NationalID || null,
                MobileNumber: MobileNumber || null,
                Right,
                Alphabet,
                TraceNumber: traceNumber,
                WalletIdentifier: phone,
                isMotor,
                withDetails
            }

            let newInquiryBase: any = {
                user: userId,
                plaque: plaqueText,
                provider: ProviderType.GHABZINO,
                source,
                phone,
                inquiryParams,
                inquiryType,
                withDetails
            }

            if (withDetails) {
                newInquiryBase = {
                    ...newInquiryBase,
                    inquiry_nationalId: NationalID || null,
                    inquiry_mobileNumber: MobileNumber || null,
                }
            }

            let fetchApiFunc: any
            let fetchParameters: TrafficFinesFetchParams = {
                WalletIdentifier: inquiryParams.WalletIdentifier,
                TraceNumber: inquiryParams.TraceNumber,
                Left,
                Right,
            }
            if (params.isMotor) {
                fetchApiFunc = params.withDetails ?
                    GhabzinoFetchClient.motorTrafficFinesInquiryByPlateNumber.bind(GhabzinoFetchClient) :
                    GhabzinoFetchClient.motorTrafficFinesInquiryByPlateNumberNoDetail.bind(GhabzinoFetchClient);
                fetchParameters = {
                    ...fetchParameters,
                    MobileNumber: params.withDetails ? params.MobileNumber! : params.phone,
                    NationalID: params.withDetails ? params.NationalID! : "",
                };
            } else {
                fetchApiFunc = params.withDetails ?
                    GhabzinoFetchClient.trafficFinesInquiryByPlateNumber.bind(GhabzinoFetchClient) :
                    GhabzinoFetchClient.trafficFinesInquiryByPlateNumberNoDetail.bind(GhabzinoFetchClient);
                fetchParameters = {
                    ...fetchParameters,
                    Mid: params.Mid,
                    Alphabet: params.Alphabet,
                    MobileNumber: params.withDetails ? params.MobileNumber! : params.phone,
                    NationalID: params.withDetails ? params.NationalID! : "",
                };
            }

            let response: GhabzinoResponse<GhabzinoTrafficInquiryResult> | undefined
            try {
                response = await fetchApiFunc(fetchParameters)
            } catch (error: any) {
                if (error instanceof HttpError && error.statusCode === status.REQUEST_TIMEOUT) {
                    if (!isPending) {
                        await
                            inquiryRepository
                                .create<GhabzinoResponse<GhabzinoTrafficInquiryResult>, GhabzinoTrafficInquiryParams>({
                                    ...newInquiryBase,
                                    inquiryStatus: InquiryStatus.PENDING,
                                    validPeriod,
                                    paid: true,
                                    balance,
                                    balance_after
                                }, session);
                    }

                    return {
                        success: 'PENDING',
                        message: error.message,
                        alreadyPaid: pendingResultFromDb?.paid
                    }

                } else {

                    if (!isPending) {
                        await
                            inquiryRepository
                                .create<GhabzinoResponse<GhabzinoTrafficInquiryResult>, GhabzinoTrafficInquiryParams>({
                                    ...newInquiryBase,
                                    inquiryStatus: InquiryStatus.FAILED,
                                    paid: false,
                                    balance,
                                }, session);
                    }

                    return {
                        success: 'FAILED',
                        message: error.message,
                    }
                }
            }

            const successCodes = ['G00000', 'GM5031'];
            const inquirySuccess = successCodes.includes(response!.Status?.Code);

            let savedResult: IInquiryResult | null = null
            if (isPending) {
                pendingResultFromDb.inquiryStatus = inquirySuccess ? InquiryStatus.SUCCESS : InquiryStatus.FAILED;
                pendingResultFromDb.inquiryResult = response
                await pendingResultFromDb.save({session})
            } else {
                savedResult = await
                    inquiryRepository
                        .create<GhabzinoResponse<GhabzinoTrafficInquiryResult>, GhabzinoTrafficInquiryParams>({
                            ...newInquiryBase,
                            inquiryStatus: inquirySuccess ? InquiryStatus.SUCCESS : InquiryStatus.FAILED,
                            validPeriod: inquirySuccess ? validPeriod : undefined,
                            inquiryResult: response,
                            paid: inquirySuccess,
                            balance,
                            balance_after: inquirySuccess ? balance_after : undefined,
                        }, session);

                if (!savedResult) {
                    throw new InternalServerError();
                }
            }

            if (inquirySuccess) {
                return {
                    success: 'SUCCESS',
                    alreadyPaid: pendingResultFromDb?.paid,
                    data: {
                        id: isPending ? pendingResultFromDb._id.toString() : savedResult!._id.toString(),
                        trackingNumber: isPending ? pendingResultFromDb.trackingNumber : savedResult!.trackingNumber!,
                    }
                };
            }

            return {
                success: 'FAILED',
                message: response?.Status?.Description || 'خطای ناشناخته‌ای رخ داده است',
            };

        } catch (error: any) {
            return handleInquiryServiceError(error);
        }
    }
}

export default TrafficFinesService