import envConfig from "@/lib/config.env";
import <PERSON>tch<PERSON><PERSON> from "@/lib/fetch-api";
import {
    BankType, GhabzinoBankResult,
    GhabzinoReportNewBillPayment,
    GhabzinoResponse,
    GhabzinoSayadiInquiryResult,
    GhabzinoTrafficInquiryResult, PayBillsRequest,
    TrafficFinesFetchParams
} from "@/lib/types";

class GhabzinoFetchClient {
    private fetchInstance: FetchApi
    private token: string

    constructor() {
        const env = envConfig()
        this.token = env.GHABZINO.TOKEN;
        this.fetchInstance = new FetchApi();
    }

    async sayadCheckInquiry(sayadId: string): Promise<GhabzinoResponse<GhabzinoSayadiInquiryResult>> {
        const env = envConfig()
        const url = env.GHABZINO.SAYAD_CHECK_INQUIRY_URL
        return this.fetchInstance.post<GhabzinoResponse<GhabzinoSayadiInquiryResult>>(url, {
            Identity: {
                Token: this.token,
            },
            Parameters: {
                SayadID: sayadId
            }
        });
    }

    async depositToIbanInquiry({
                                   depositNumber,
                                   bank,
                                   accountTypeName
                               }: Partial<BankType>): Promise<GhabzinoResponse<GhabzinoBankResult>> {
        const env = envConfig()
        const url = env.GHABZINO.SHEBA_INQUIRY_ACCOUNT_TO_SHEBA
        return this.fetchInstance.post<GhabzinoResponse<GhabzinoBankResult>>(url, {
            Identity: {
                Token: this.token,
            },
            Parameters: {
                AccountNumber: depositNumber,
                AccountTypeName: accountTypeName,
                BankName: bank
            }
        });
    }

    async cardToIbanInquiry({
                                cardNumber,
                                accountTypeName
                            }: Partial<BankType>): Promise<GhabzinoResponse<GhabzinoBankResult>> {
        const env = envConfig()
        const url = env.GHABZINO.SHEBA_INQUIRY_CARD_TO_SHEBA
        return this.fetchInstance.post<GhabzinoResponse<GhabzinoBankResult>>(url, {
            Identity: {
                Token: this.token,
            },
            Parameters: {
                CardNumber: cardNumber,
                AccountTypeName: accountTypeName,
            }
        });
    }

    async motorTrafficFinesInquiryByPlateNumber(fetchParams: TrafficFinesFetchParams): Promise<GhabzinoResponse<GhabzinoTrafficInquiryResult>> {
        const env = envConfig()
        const url = env.GHABZINO.MOTOR_TRAFFIC_FINES_INQUIRY_BY_PLATE_NUMBER_URL
        return this.fetchInstance.post<GhabzinoResponse<GhabzinoTrafficInquiryResult>>(url, {
            Identity: {
                Token: this.token,
            },
            Parameters: fetchParams
        });
    }

    async motorTrafficFinesInquiryByPlateNumberNoDetail(fetchParams: TrafficFinesFetchParams): Promise<GhabzinoResponse<GhabzinoTrafficInquiryResult>> {
        const env = envConfig()
        const url = env.GHABZINO.MOTOR_TRAFFIC_FINES_INQUIRY_BY_PLATE_NUMBER_NO_DETAIL_URL
        return this.fetchInstance.post<GhabzinoResponse<GhabzinoTrafficInquiryResult>>(url, {
            Identity: {
                Token: this.token,
            },
            Parameters: fetchParams
        });
    }

    async trafficFinesInquiryByPlateNumber(fetchParams: TrafficFinesFetchParams): Promise<GhabzinoResponse<GhabzinoTrafficInquiryResult>> {
        const env = envConfig()
        const url = env.GHABZINO.TRAFFIC_FINES_INQUIRY_BY_PLATE_NUMBER_URL
        return this.fetchInstance.post<GhabzinoResponse<GhabzinoTrafficInquiryResult>>(url, {
            Identity: {
                Token: this.token,
            },
            Parameters: fetchParams
        });
    }

    async trafficFinesInquiryByPlateNumberNoDetail(fetchParams: TrafficFinesFetchParams): Promise<GhabzinoResponse<GhabzinoTrafficInquiryResult>> {

        const env = envConfig()
        const url = env.GHABZINO.TRAFFIC_FINES_INQUIRY_BY_PLATE_NUMBER_NO_DETAIL_URL
        return this.fetchInstance.post<GhabzinoResponse<GhabzinoTrafficInquiryResult>>(url, {
            Identity: {
                Token: this.token,
            },
            Parameters: fetchParams
        });
    }

    async reportNewBillPayment(fetchParams: PayBillsRequest): Promise<GhabzinoResponse<GhabzinoReportNewBillPayment>> {
        const env = envConfig()
        const url = env.GHABZINO.REPORT_NEW_BILL_PAYMENT_URL
        return this.fetchInstance.post<GhabzinoResponse<GhabzinoReportNewBillPayment>>(url, {
            Identity: {
                Token: this.token,
            },
            Parameters: fetchParams
        });
    }

}

export default new GhabzinoFetchClient();