import {model, Model, models, Schema} from 'mongoose';
import {
    CategorySlugEnum,
    InquiryPolicyEnum,
    InquiryTypeEnum,
    IService,
    ProviderType,
    SettingsValueTypeEnum
} from "@/lib/types";
import {SOURCE_NAME} from "@/lib/constants";

const ServiceSchema = new Schema<IService>(
    {
        key: {
            type: String,
            enum: Object.values(InquiryTypeEnum),
            required: true,
            index: true,
        },
        provider: {
            type: String,
            enum: Object.values(ProviderType),
            required: true,
        },
        value: {
            type: String,
            enum: Object.values(SettingsValueTypeEnum),
            default: SettingsValueTypeEnum.ACTIVE,
        },
        env: {
            type: String,
            enum: ['development', 'production'],
            index: true
        },
        title: {
            type: String,
            required: true
        },
        validPeriod: {
            type: Number,
            required: true
        },
        inquiryPolicy: {
            type: String,
            enum: Object.values(InquiryPolicyEnum),
            required: true,
        },
        source: {
            type: String,
            enum: [SOURCE_NAME],
            default: SOURCE_NAME,
            index: true
        },
        price: {
            type: Number,
            required: true
        },
        category: {
            type: String,
            enum: Object.values(CategorySlugEnum),
            required: true,
        },
    },
    {
        timestamps: true,
        collection: 'settings',
        optimisticConcurrency: true,
    }
);

const ServiceModel: Model<IService> =
    (models.settings) ||
    model<IService>("settings", ServiceSchema);

export default ServiceModel;

