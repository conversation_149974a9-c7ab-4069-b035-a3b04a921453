import {SignJWT, jwtVerify, JWTPayload, decodeJwt} from "jose";
import envConfig from "@/lib/config.env";
import {InternalServerError} from "@/lib/error";
import {SMS_TOKEN_IN_DEVELOPMENT} from "@/lib/constants";
import {JWTUserPayload} from "@/lib/types";
import {v4 as uuidv4} from 'uuid';


class TokenService {
    // متد برای تولید توکن
    async generateJwtToken(payload: JWTUserPayload = {}) {
        const env = envConfig();
        const secret = env.JWT.SECRET;
        const expiresIn = env.JWT.EXPIRES_IN;
        if (!secret || !expiresIn) {
            throw new InternalServerError("secret یا expires_in خالی است.");
        }
        const expiresAt = new Date(Date.now() + Number(expiresIn) * 60 * 60 * 1000)
        const secretKey = new TextEncoder().encode(secret);
        const token = await new SignJWT(payload)
            .setProtectedHeader({alg: "HS256"})
            .setExpirationTime(expiresAt)
            .sign(secretKey);

        return {token, expiresAt};
    }

    decodeJwtToken(token: string) {
        try {
            return decodeJwt(token);  // Decode the JWT token
        } catch (error) {
            console.error(error);
            return null
        }
    }

    isTokenExpired(token: string, bufferInMinute: number = 0) {
        const decoded = this.decodeJwtToken(token);  // Decode the JWT token
        if (decoded && decoded.exp) {
            const bufferTime = bufferInMinute * 60 * 1000; // 5 minutes in milliseconds
            const currentTime = Date.now();
            const tokenExpiryTime = decoded.exp * 1000;
            return currentTime >= (tokenExpiryTime - bufferTime);
        }
        return true;
    }

    generateOtpToken() {
        const env = envConfig()
        const OTP_EXPIRES_IN = env.OTP_EXPIRES_IN;
        if (!OTP_EXPIRES_IN) {
            throw new InternalServerError("OTP_EXPIRES_IN خالی است");
        }
        const otpExpires = new Date(Date.now() + Number(OTP_EXPIRES_IN) * 60 * 1000)
        let otpToken: string | undefined;
        if (env.NODE_ENV === 'development') {
            otpToken = SMS_TOKEN_IN_DEVELOPMENT
        } else {
            otpToken = Math.floor(10000 + Math.random() * 90000).toString(); // 5-digit OTP
        }

        return {
            otpToken: otpToken,
            otpExpiresIn: otpExpires,
        };
    }

    generateUUID() {
        return uuidv4();
    }

    // متد برای تایید اعتبار توکن
    async verifyJwtToken<T = JWTPayload>(token: string | undefined): Promise<T | null> {
        const env = envConfig();
        const secret = env.JWT.SECRET;
        if (!secret) {
            throw new InternalServerError();
        }
        const secretKey = new TextEncoder().encode(secret);
        if (!token) {
            return null;
        }
        try {
            const {payload} = await jwtVerify(token, secretKey);
            return payload as T;
        } catch (error: any) {
            console.error("JWT verification failed:", error.message);
            return null;
        }
    }

}

export default new TokenService();

