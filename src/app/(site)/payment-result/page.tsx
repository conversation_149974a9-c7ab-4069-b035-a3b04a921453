import PaymentResult from "@/components/depo/result/payment-result";
import Container from "@/components/common/container";

type Props = {
    searchParams: Promise<{ Authority: string, Status: string }>
}

export default async function DepoResultPage({searchParams}: Props) {
    const sParams = await searchParams
    const Authority = sParams.Authority;
    const Status = sParams.Status;

    return (
        <Container>
            <PaymentResult Status={Status} Authority={Authority}/>
        </Container>
    );
}
