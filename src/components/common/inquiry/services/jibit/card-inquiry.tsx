import {InquiryTypeEnum, ServiceComponentProps} from "@/lib/types";
import {Form, FormControl, FormField, FormItem, FormMessage} from "@/components/ui/form";
import IranBankCardNumber from "@/components/common/bank-card-input";
import CustomButton from "@/components/common/custom-button";
import {useForm} from "react-hook-form";
import {iranBankCardArraySchema, IranBankCardType} from "@/lib/zod-schemas";
import {zodResolver} from "@hookform/resolvers/zod";
import InquiryPrice from "@/components/common/inquiry/inquiry-price";

export default function CardInquiryForm({isLoading, onSubmit, price}: ServiceComponentProps) {

    const formCardToDeposit = useForm<IranBankCardType>({
        resolver: zodResolver(iranBankCardArraySchema),
        mode: 'onSubmit',
        defaultValues: {
            cardNumber: ["", "", "", ""]
        },
    })

    async function handleSubmit(values: any) {
        if (isLoading) return;
        onSubmit?.(values, InquiryTypeEnum.Jibit_CardInquiry)
    }

    return (
        <>
            <Form {...formCardToDeposit}>
                <form
                    onSubmit={formCardToDeposit.handleSubmit((values) => handleSubmit(values))}
                    className="space-y-6">
                    <FormField
                        control={formCardToDeposit.control}
                        name="cardNumber"
                        render={({field}) => (
                            <FormItem className='w-full'>
                                <p className='font-light mb-7 text-neutral-700 text-sm md:text-base'>شماره
                                    کارت را
                                    وارد کنید، سپس دکمه
                                    استعلام را
                                    انتخاب کنید
                                </p>
                                <FormControl>
                                    <IranBankCardNumber
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage className='text-sm'/>
                            </FormItem>
                        )}
                    />
                    <CustomButton
                        loading={isLoading}
                        disabled={isLoading}
                        variants='blue'
                        className="mt-1"
                        type="submit">استعلام
                    </CustomButton>
                </form>
            </Form>
            <InquiryPrice price={price}/>
        </>
    );
}
