import {BankInfoStatus} from "@/lib/types";

export function removeComma(value: string) {
    if (!value) return;
    return value.split(',').join("")
}

export function formatWithComma(value: string) {
    if (!value || isNaN(Number(value))) return value
    return Number(value.replace(/,/g, "")).toLocaleString();
}

export function miladiToShamsi(value: string) {
    const date = new Date(value);
    const shamsiDate = new Intl.DateTimeFormat('fa-IR').format(date);
    return shamsiDate
}

export const decodeSearchParams = (searchParams: Record<string, string | string[]>): Record<string, string | string[]> => {
    return Object.fromEntries(
        Object.entries(searchParams).map(([key, value]) => [
            key,
            Array.isArray(value) ? value.map(decodeURIComponent) : decodeURIComponent(value),
        ])
    );
}


export function translateBankInfoStatus(status: BankInfoStatus): string {
    switch (status) {
        case BankInfoStatus.ACTIVE:
            return "فعال";
        case BankInfoStatus.BLOCK_WITH_DEPOSIT:
            return "حساب بلاک شده است اما قابلیت واریز دارد.";
        case BankInfoStatus.BLOCK_WITHOUT_DEPOSIT:
            return "حساب بلاک شده و قابلیت واریز ندارد";
        case BankInfoStatus.IDLE:
            return "راکد";
        case BankInfoStatus.UNKNOWN:
            return "نامشخص";
    }
}




