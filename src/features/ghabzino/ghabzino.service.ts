import {BaseGhabzino} from "@/features/ghabzino/types/base-ghabzino";
import {IInquiryService} from "@/lib/types";

class GhabzinoService implements BaseGhabzino {
    private _sayadi?: any;
    private _trafficFines?: any;
    private _payBillPortal?: any;
    private _depositToIban?: any;
    private _cardToIban?: any;

    async sayadi(): Promise<IInquiryService<any, any>> {
        if (!this._sayadi) {
            const sayadiModule = await import("@/features/ghabzino/services/sayadi.service");
            this._sayadi = new sayadiModule.default();
        }
        return this._sayadi;
    }

    async trafficFines(): Promise<IInquiryService<any, any>> {
        if (!this._trafficFines) {
            const trafficFinesModule = await import("@/features/ghabzino/services/traffic-fines.service");
            this._trafficFines = new trafficFinesModule.default();
        }
        return this._trafficFines;
    }

    async payBillPortal(): Promise<IInquiryService<any, any>> {
        if (!this._payBillPortal) {
            const payBillPortalModule = await import("@/features/ghabzino/services/pay-bill-portal.service");
            this._payBillPortal = new payBillPortalModule.default();
        }
        return this._payBillPortal;
    }

    async depositToIban(): Promise<IInquiryService<any, any>> {
        if (!this._depositToIban) {
            const depositToIbanModule = await import("@/features/ghabzino/services/deposit-to-iban.service");
            this._depositToIban = new depositToIbanModule.default();
        }
        return this._depositToIban;
    }

    async cardToIban(): Promise<IInquiryService<any, any>> {
        if (!this._cardToIban) {
            const cardToIbanModule = await import("@/features/ghabzino/services/card-to-iban.service");
            this._cardToIban = new cardToIbanModule.default();
        }
        return this._cardToIban;
    }

}

export default new GhabzinoService();