import {Card, CardContent} from "@/components/ui/card";
import {InquiryTypeEnum, VehiclePaymentDetails} from "@/lib/types";
import DisplayPlaque from "@/components/common/display-plaque";
import {formatWithComma} from "@/utils/helpers";
import CustomButton from "@/components/common/custom-button";

type Props = {
    inquiry: VehiclePaymentDetails
}


const inquiryTypeToFarsi = (type: InquiryTypeEnum) => {
    switch (type) {
        case InquiryTypeEnum.Ghanzino_KhalafiMotor:
            return 'استعلام خلافی موتور'
        case InquiryTypeEnum.Ghanzino_KhalafiKhodro:
            return 'استعلام خلافی خودرو'
        default:
            return '-'
    }
}

export default function GhabzinoTrafficFineContent({inquiry}: Props) {

    const {middle, alphabet, right, left} = inquiry.plaque
    const isMotor = inquiry.isMotor
    return (
        <div className="mb-4">
            <Card className="overflow-hidden py-0 gap-y-4">
                <div className="flex px-4 py-2 bg-neutral-100 items-center justify-between">
                    <h3 className="text-base font-semibold text-neutral-500">{inquiryTypeToFarsi(inquiry.inquiryType)}</h3>
                </div>
                <CardContent className=" px-2 pb-4">
                    <div className="grid gap-2 text-right">
                        <div className='w-full max-w-[350px] mx-auto'>

                            <DisplayPlaque left={left || ""} right={right || ""} middle={middle || ""}
                                           alphabet={alphabet || ""}
                                           isMotor={isMotor}/>
                        </div>
                        <div className="grid gap-2 px-2 md:px-10 mt-5">
                            {inquiry?.phoneNumber &&
                                <div className="flex items-center justify-between">
                                                        <span
                                                            className="font-medium text-base">شماره موبایل:</span>
                                    <div className='flex items-center gap-x-1'>
                                        <span>{inquiry?.phoneNumber}</span>
                                    </div>
                                </div>}
                            {inquiry?.nationalId &&
                                <div className="flex justify-between">
                                    <span className="font-medium">کد ملی:</span>
                                    <span>{inquiry?.nationalId}</span>
                                </div>}
                            {(inquiry.withDetails !== undefined) && <div className="flex justify-between">
                                <span className="font-medium">با جزییات:</span>
                                <span dir="ltr" className="text-left">
                                                       {inquiry.withDetails ? 'بله' : 'خیر'}
                                                    </span>
                            </div>
                            }
                            {inquiry?.dateInquiry && <div className="flex justify-between">
                                <span className="font-medium">تاریخ استعلام:</span>
                                <span dir="ltr" className="text-left">
                                                        {inquiry?.dateInquiry}
                                                    </span>
                            </div>
                            }
                            {inquiry?.TotalAmount && <div className="flex justify-between">
                                <span className="font-medium">مبلغ کل:</span>
                                <div>
                                            <span dir="ltr" className="text-left">
                                                        {formatWithComma(inquiry.TotalAmount.toString())}
                                                    </span>
                                    <span className='mr-1 text-sm'>تومان</span>
                                </div>
                            </div>
                            }
                            {inquiry?.Amount && <div className="flex justify-between">
                                <span className="font-medium">مبلغ کل:</span>

                                <div>
                                            <span dir="ltr" className="text-left">
                                                        {formatWithComma(inquiry.Amount.toString())}
                                                    </span>
                                    <span className='mr-1 text-sm'>تومان</span>
                                </div>
                            </div>
                            }
                            {inquiry?.description && <div className="">
                                <p className='text-center'>
                                    {formatWithComma(inquiry.description)}
                                </p>
                            </div>
                            }
                            <div
                                className="flex gap-x-5 flex-col justify-start mt-5 font-semibold gap-y-1 items-start">
                                <CustomButton href={inquiry.inquiryUrl}
                                              variants='blue'>مشاهده</CustomButton>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    )

}


