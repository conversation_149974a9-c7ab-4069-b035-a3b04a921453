import {Fragment} from "react";
import {<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>eader} from "@/components/ui/card";
import CustomButton from "@/components/common/custom-button";
import {HOME_PATH} from "@/lib/routes";
import {JibitResultType} from "@/lib/types";
import {banks} from "@/lib/data/jibit-bank-list";
import DataJibitActionButtons from "@/components/result/data-jibit-action-buttons";
import {translateBankInfoStatus} from "@/utils/helpers";

type Props = {
    jibitResult: JibitResultType
    newInquiryurl?: string
}

export default function JibitBankInquiry({jibitResult, newInquiryurl}: Props) {

    const bankInfo = banks.find((item) => item.bankId === jibitResult.bank);

    return (

        <Card className="w-full !h-fit max-w-lg lg:max-w-2xl bg-white rounded-lg overflow-hidden">
            <CardHeader className="flex flex-row justify-between items-start pb-2 pt-6 px-6">
                <h2 className="text-2xl font-bold">نتیجه استعلام</h2>
                {jibitResult?.bank && <div className="flex items-center">
                    {
                        bankInfo?.icon
                    }
                </div>}
            </CardHeader>

            <CardContent className="px-6" dir='rtl'>
                <div className="space-y-4">
                    {jibitResult?.bank && <div className="flex gap-x-5 items-center">
                        <span className="">نام بانک:</span>
                        <span
                            className="text-gray-600 font-semibold">{bankInfo?.bankNameFarsi}</span>
                    </div>}

                    {jibitResult?.ownerName && <div className="flex gap-x-5 items-center">
                        <span className="">صاحب حساب:</span>
                        <span
                            className="text-gray-600 font-semibold">{jibitResult.ownerName}</span>
                    </div>}

                    {(jibitResult?.owners && jibitResult.owners.length > 0) &&
                        <div className="flex gap-x-5 items-center">
                            <span className="">نام صاحب حساب:</span>
                            <div>
                                {
                                    jibitResult.owners.map((ownerName, index) => {
                                        return (
                                            <Fragment key={index}>
                                                <span className="text-gray-600 font-semibold">{ownerName}</span>
                                                {index !== jibitResult.owners.length - 1 && (
                                                    <span className='ml-1'>,</span>)}
                                            </Fragment>

                                        )
                                    })
                                }
                            </div>
                        </div>}

                    {jibitResult?.depositNumber && <div className="flex gap-x-5 items-center">
                        <span className="">شماره حساب:</span>
                        <span className="text-gray-600 font-semibold">{jibitResult.depositNumber}</span>
                    </div>}
                    {jibitResult?.status && <div className="flex gap-x-5 items-center">
                        <span className="">وضعیت حساب:</span>
                        <span
                            className="text-gray-600 font-semibold">{translateBankInfoStatus(jibitResult.status)}</span>
                    </div>}
                    {jibitResult?.iban && <div className="flex gap-x-5 items-center">
                        <span className="">شماره شبا:</span>
                        <span className="text-gray-600 font-semibold">{jibitResult.iban}</span>
                    </div>}
                </div>

                {/*<div className="border-t border-gray-200 my-6"></div>*/}

                <DataJibitActionButtons bankInfo={bankInfo} jibitResult={jibitResult}/>
                {/*<div className="border-t border-gray-200 my-6"></div>*/}
                {/*<div className="mt-6">*/}
                {/*    <h3 className="text-center mb-4 font-semibold">اشتراک گذاری</h3>*/}
                {/*    <div className="flex flex-col gap-4">*/}
                {/*        <CustomButton variants='outline-orange'>*/}
                {/*            <MessageSquare className="h-5 w-5"/>*/}
                {/*            <span className='mx-2'>تلگرام</span>*/}
                {/*        </CustomButton>*/}
                {/*        <CustomButton variants='outline-orange'>*/}
                {/*            <Download className="h-5 w-5"/>*/}
                {/*            <span className='mx-2'>دانلود</span>*/}
                {/*        </CustomButton>*/}
                {/*    </div>*/}
                {/*</div>*/}
            </CardContent>
            <div className="border-t border-gray-200 my-6"></div>
            <CardFooter className="px-6 pb-6">
                <CustomButton href={newInquiryurl || HOME_PATH} variants='green'>استعلام جدید</CustomButton>
            </CardFooter>
        </Card>
    )
}
