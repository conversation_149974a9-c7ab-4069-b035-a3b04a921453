'use server'

import {ActionResult, Category, ICategory} from "@/lib/types";
import {connectToDatabase} from "@/lib/mongodb";
import {getCurrentUser, handleActionErrorResponse} from "@/utils/helpers-server";
import {ForbiddenError, NotFoundError, UnauthorizedError} from "@/lib/error";
import {v4 as uuidv4} from 'uuid'
import path from 'path'
import {writeFile} from 'fs/promises'
import categoryRepository from "@/features/category/category.repository";

export async function getOrSeedAppCategories(active?: boolean): Promise<ActionResult<Category[]>> {
    try {
        await connectToDatabase()

        let categories: ICategory[] = []
        categories = await categoryRepository.getCategories();
        if (categories.length === 0) {
            categories = await categoryRepository.seedCategories()
        }

        if (active) {
            categories = categories.filter(category => category.active)
        }

        return {
            success: true,
            data: categories.map((category) => {
                return {
                    title: category.title,
                    active: category.active,
                    slug: category.slug,
                    path: category.path,
                    description: category.description,
                }
            })
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}

export async function seedCategories(): Promise<ActionResult<Category[]>> {
    try {
        await connectToDatabase()

        let servicesFromDb = await categoryRepository.getCategories();

        if (servicesFromDb.length === 0) {
            servicesFromDb = await categoryRepository.seedCategories();
        }

        return {
            success: true,
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}

export async function upsertCategory(formData: FormData): Promise<ActionResult> {

    try {
        await connectToDatabase()

        const currentUser = await getCurrentUser();

        if (!currentUser) {
            throw new UnauthorizedError()
        }

        if (!currentUser?.admin) {
            throw new ForbiddenError()
        }

        const title = formData.get('title') as string
        const slug = formData.get('slug') as string
        const isActive = formData.get('active') === 'true'
        const id = formData.get('id') as string | undefined
        const image = formData.get('image') as File | null

        console.log({title, slug, id, image})

        let imagePath: string | undefined = undefined

        if (image) {
            const bytes = await image.arrayBuffer()
            const buffer = Buffer.from(bytes)
            const fileName = `${uuidv4()}-${image.name}`
            const filePath = path.join(process.cwd(), 'public/uploads', fileName)
            await writeFile(filePath, buffer)
            imagePath = `/uploads/${fileName}`
        }

        console.log({title, id, isActive, imagePath})

        if (id) {
            await categoryRepository.update({
                id,
                title: title,
                imagePath,
                slug,
            })
        } else {
            await categoryRepository.create({
                imagePath,
                title,
                active: isActive,
                slug,
            })
        }

        return {
            success: true
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}

export async function toggleCategoryActive(id: string): Promise<ActionResult> {
    try {
        await connectToDatabase()

        const currentUser = await getCurrentUser();

        if (!currentUser) {
            throw new UnauthorizedError()
        }

        if (!currentUser?.admin) {
            throw new ForbiddenError()
        }

        const categoryFromDb = await categoryRepository.findById(id);

        if (!categoryFromDb) {
            throw new NotFoundError(`دسته بندی با id ${id} یافت نشد`)
        }

        categoryFromDb.active = !categoryFromDb.active;

        await categoryFromDb.save()

        return {
            success: true,
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}