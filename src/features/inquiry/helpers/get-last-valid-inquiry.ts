import {IInquiryResult, InquiryStatus, InquiryType, InquiryTypeEnum} from "@/lib/types";
import {ClientSession} from "mongoose";
import inquiryRepository from "@/features/inquiry/inquiry.repository";
import {convertPlaquePartsToString} from "@/utils/helpers-server";

export async function getLastValidInquiry({source, phone, params}: {
    params: InquiryType,
    phone: string,
    source: string
}, session: ClientSession): Promise<IInquiryResult | null> {
    const {inquiryType, cardNumber, bank, depositNumber, iban, sayadId, accountTypeName} = params;
    let inquiryResult: IInquiryResult | null = null;
    switch (inquiryType) {
        case InquiryTypeEnum.Jibit_CardToDeposit:
            inquiryResult = await inquiryRepository.findOneJibitCardToDeposit({
                cardNumber: cardNumber!,
                source,
                phone
            }, session);
            break;
        case InquiryTypeEnum.Jibit_CardToIban:
            inquiryResult = await inquiryRepository.findOneJibitCardToIban({
                cardNumber: cardNumber!,
                phone,
                source
            }, session);
            break;
        case InquiryTypeEnum.Jibit_DepositToIban:
            inquiryResult = await inquiryRepository.findOneJibitDepositToIban({
                bank: bank!,
                source,
                depositNumber: depositNumber!,
                phone
            }, session);
            break;
        case InquiryTypeEnum.Ghanzino_DepositToIban:
            inquiryResult = await inquiryRepository.findOneGhabzinoDepositToIban({
                bank: bank!,
                accountTypeName: accountTypeName!,
                source,
                depositNumber: depositNumber!,
                phone
            }, session);
            break;
        case InquiryTypeEnum.Ghanzino_CardToIban:
            inquiryResult = await inquiryRepository.findOneGhabzinoCardToIban({
                cardNumber: cardNumber!,
                accountTypeName: accountTypeName!,
                source,
                phone
            }, session);
            break;
        case InquiryTypeEnum.Jibit_IbanInquiry:
            inquiryResult = await inquiryRepository.findOneJibitInquireIban({
                iban: iban!,
                source,
                phone: phone,
            }, session)
            break;
        case InquiryTypeEnum.Jibit_CardInquiry:
            inquiryResult = await inquiryRepository.findOneJibitInquireCard({
                cardNumber: cardNumber!,
                source,
                phone
            }, session)
            break;
        case InquiryTypeEnum.Ghabzino_SayadCheckInquiry:
            inquiryResult = await inquiryRepository.findOneGhabzinoInquireSayadId({
                phone,
                source,
                sayadId: sayadId!
            }, session)
            break;
        case InquiryTypeEnum.Ghanzino_KhalafiKhodro:
        case InquiryTypeEnum.Ghanzino_KhalafiMotor:
            const {withDetails, middle, right, alphabet, left, phoneNumber, nationalCode} = params
            const plaqueText = convertPlaquePartsToString({
                left: left!,
                right: right!,
                alphabet,
                middle,
            })

            inquiryResult = await inquiryRepository.findOneGhabzinoTrafficFineInquiry({
                phone,
                inquiryStatus: InquiryStatus.SUCCESS,
                inquiryType,
                source,
                nationalID: nationalCode || null,
                plaque: plaqueText,
                phoneNumber: phoneNumber || null,
                withDetails: withDetails === 'true'
            }, session)
            break;
    }

    return inquiryResult;
}