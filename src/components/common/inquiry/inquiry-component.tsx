'use client'

import CardInquiryForm from "@/components/exchange/card-inquiry-form";
import {CategorySlugEnum, SettingsValueTypeEnum} from "@/lib/types";
import {useService} from "@/lib/hooks/useService";
import {useMemo} from "react";
import NoServiceCard from "@/components/common/inquiry/no-service-card";
import ReturnLink from "@/components/common/returnLink";

type Props = {
    categorySlug: CategorySlugEnum;
    categoryTitle?: string;
}

export default function InquiryComponent({categorySlug, categoryTitle}: Props) {
    const {services} = useService();

    const validServices = useMemo(() => {
        return services.filter((item) => item.value === SettingsValueTypeEnum.ACTIVE && item.category === categorySlug)
    }, [services]);

    return (
        validServices.length > 0 ? (
            <div className="bg-white h-fit min-h-[300px] rounded-2xl shadow-xl px-3 py-7 pt-5 md:p-8 relative">
                <div
                    className="absolute -top-5 right-1/2 w-[150px] h-[40px] transform text-center translate-x-1/2 bg-white px-3 md:px-6 py-2 rounded-full text-xs sm:text-sm">
                    <div className='flex items-center justify-center'>
                        <ReturnLink/>
                    </div>
                </div>
                {
                    categoryTitle &&
                    <h3 className='text-sm md:text-base text-center mt-3 text-neutral-700'>{categoryTitle}</h3>
                }
                <CardInquiryForm services={validServices}/>
            </div>
        ) : (
            <NoServiceCard/>
        )
    );
}
