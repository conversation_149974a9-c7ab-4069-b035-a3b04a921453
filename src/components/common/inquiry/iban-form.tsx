'use client'

import {InquiryTypeEnum, ServiceComponentProps} from "@/lib/types";
import {Form, FormControl, FormField, FormItem, FormMessage} from "@/components/ui/form";
import CustomButton from "@/components/common/custom-button";
import {useForm} from "react-hook-form";
import {IbanInquirySchema, IbanInquiryType} from "@/lib/zod-schemas";
import {zodResolver} from "@hookform/resolvers/zod";
import IbanInput from "@/components/common/iban-input";
import InquiryPrice from "@/components/common/inquiry/inquiry-price";

type Props = ServiceComponentProps & {
    inquiryType: InquiryTypeEnum,
    btnText: string,
}

export default function IbanForm({isLoading, onSubmit, price, inquiryType, btnText}: Props) {

    const formCardToIban = useForm<IbanInquiryType>({
        resolver: zodResolver(IbanInquirySchema),
        mode: 'onSubmit',
        defaultValues: {
            iban: ""
        },
    })

    async function handleSubmit(values: any) {
        if (isLoading) return;
        onSubmit?.(values, inquiryType);
    }

    return (
        <>
            <Form {...formCardToIban}>
                <form
                    onSubmit={formCardToIban.handleSubmit((values) => handleSubmit(values))}
                    className="space-y-6">
                    <FormField
                        control={formCardToIban.control}
                        name="iban"
                        render={({field}) => (
                            <FormItem className='w-full'>
                                <p className='font-light mb-7 text-neutral-700 text-sm md:text-base'>شماره
                                    شبا را
                                    وارد کنید، سپس دکمه
                                    استعلام را
                                    انتخاب کنید
                                </p>
                                <FormControl>
                                    <IbanInput
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage className='text-sm'/>
                            </FormItem>
                        )}
                    />
                    <CustomButton
                        loading={isLoading}
                        disabled={isLoading}
                        variants='blue'
                        className="mt-1"
                        type="submit">{btnText}
                    </CustomButton>
                </form>
            </Form>
            <InquiryPrice price={price}/>
        </>
    );
}
