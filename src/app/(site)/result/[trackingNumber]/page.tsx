import Container from "@/components/common/container";
import JibitBankInquiry from "@/components/result/jibit-bank-inquiry";
import {IS_NEW, TRACKING_NUMBER_NAME} from "@/lib/constants";
import {getInquiryByTrackingNumber} from "@/features/inquiry/inquiry.action";
import status from "http-status";
import {notFound, redirect} from "next/navigation";
import {ERROR_PATH} from "@/lib/routes";
import BankInquiryError from "@/components/result/bank-inquiry-error";
import {
    GhabzinoBankType,
    GhabzinoResultType,
    InquiryTypeEnum,
    JibitResultType,
    VehiclePaymentDetails
} from "@/lib/types";
import SayadiCheckInquiry from "@/components/result/sayad-check-inquiry";
import TrafficFineInquiry from "@/components/result/traffic-fine-inquiry";
import GhabzinoBankInquiry from "@/components/result/ghabzino-bank-inquiry";

type Props = {
    params: Promise<{
        [TRACKING_NUMBER_NAME]: string
    }>
    searchParams: Promise<{
        inquiryType: string, inquiryPath: string,
        [IS_NEW]: 'true' | 'false'
    }>
}


export default async function ResultPage({params, searchParams}: Props) {
    const {trackingNumber} = await params;
    const {inquiryType, inquiryPath, isNew} = (await searchParams)

    const newInquiryUrl = (inquiryPath && inquiryType) ? `${inquiryPath}?selected=${inquiryType}` : '/'

    if (!trackingNumber) {
        notFound();
    }


    const actionResult = await getInquiryByTrackingNumber(trackingNumber);
    let errorMessage = ''

    if (!actionResult.success && actionResult.status === status.INTERNAL_SERVER_ERROR) {
        redirect(ERROR_PATH)
    }

    console.log(actionResult?.data)

    if (!actionResult.success && actionResult.status === status.NOT_FOUND) {
        redirect('/not-found');
    }

    if (!actionResult.success) {
        errorMessage = actionResult.message ?? 'مشکلی ناخواسته ای پیش آمده';
    }

    const isJibitBankInquiry =
        actionResult.data?.inquiryType === InquiryTypeEnum.Jibit_DepositToIban ||
        actionResult.data?.inquiryType === InquiryTypeEnum.Jibit_CardToIban ||
        actionResult.data?.inquiryType === InquiryTypeEnum.Jibit_CardToDeposit ||
        actionResult.data?.inquiryType === InquiryTypeEnum.Jibit_IbanInquiry ||
        actionResult.data?.inquiryType === InquiryTypeEnum.Jibit_CardInquiry

    const isGhabzinoBankInquiry =
        actionResult.data?.inquiryType === InquiryTypeEnum.Ghanzino_DepositToIban ||
        actionResult.data?.inquiryType === InquiryTypeEnum.Ghanzino_CardToIban

    const isSayadCheckInquiry = actionResult.data?.inquiryType === InquiryTypeEnum.Ghabzino_SayadCheckInquiry
    const isGhabzinoTrafficFineInquiry =
        (actionResult.data?.inquiryType === InquiryTypeEnum.Ghanzino_KhalafiKhodro || actionResult.data?.inquiryType === InquiryTypeEnum.Ghanzino_KhalafiMotor)

    return (
        <Container>
            {errorMessage && <BankInquiryError message={errorMessage}/>}
            {actionResult.success && (
                <>
                    {
                        isJibitBankInquiry &&
                        <JibitBankInquiry
                            newInquiryurl={newInquiryUrl}
                            jibitResult={(actionResult.data as JibitResultType)!}/>
                    }
                    {
                        isGhabzinoBankInquiry &&
                        <GhabzinoBankInquiry
                            newInquiryurl={newInquiryUrl}
                            result={(actionResult.data as GhabzinoBankType)!}/>
                    }
                    {
                        isSayadCheckInquiry &&
                        <SayadiCheckInquiry
                            newInquiryurl={newInquiryUrl}
                            descriptipn={(actionResult.data as GhabzinoResultType)?.description}
                            sayadId={(actionResult.data as GhabzinoResultType)?.sayadId}
                            sayadiResult={(actionResult.data as GhabzinoResultType).sayadiResult!}
                        />
                    }
                    {
                        isGhabzinoTrafficFineInquiry &&
                        <TrafficFineInquiry
                            isNew={isNew}
                            inquiryPath={inquiryPath}
                            newInquiryUrl={newInquiryUrl}
                            inquiryDetail={actionResult.data as VehiclePaymentDetails}
                        />
                    }
                </>
            )
            }
        </Container>
    );
}
