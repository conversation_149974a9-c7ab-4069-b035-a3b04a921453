import mongoose from "mongoose";
import envConfig from "@/lib/config.env";

interface MongooseGlobal {
    conn: typeof mongoose | null;
    promise: Promise<typeof mongoose> | null;
}

const globalWithMongoose = global as typeof globalThis & {
    mongoose: MongooseGlobal;
};

if (!globalWithMongoose.mongoose) {
    globalWithMongoose.mongoose = {conn: null, promise: null};
}

export async function connectToDatabase() {
    const env = envConfig();
    const MONGODB_URI = env.MONGODB_URI!;

    if (globalWithMongoose.mongoose.conn) {
        return globalWithMongoose.mongoose.conn;
    }

    if (!globalWithMongoose.mongoose.promise) {
        console.log("🔄 Connecting to MongoDB...");
        globalWithMongoose.mongoose.promise = mongoose
            .connect(MONGODB_URI)
            .then((mongoose) => {
                console.log("🚀 MongoDB Connected!");
                return mongoose;
            })
            .catch((err) => {
                console.error("❌ MongoDB connection error:", err);
                throw err;
            });
    }

    globalWithMongoose.mongoose.conn = await globalWithMongoose.mongoose.promise;
    return globalWithMongoose.mongoose.conn;
}
