'use client'

import DisplayAuthStatus from "@/components/header/display-auth-status";
import {useApplication} from "@/lib/hooks/useApplication";
import Link from "next/link";
import {HOME_PATH} from "@/lib/routes";

export default function Header() {
    const {application} = useApplication()
    return (
        <header className="flex justify-between bg-white items-center py-5 px-4 md:px-24 border-b">
            <Link href={HOME_PATH} className="text-lg md:text-2xl font-bold">{application?.title}</Link>
            <span></span>
            <DisplayAuthStatus/>
        </header>
    );
}
