import {
    BankInquiryType,
    BaseParamsType,
    GenericResponse,
    IInquiryService, InquiryStatus, InquiryTypeEnum, JibitFailedResultType,
    ProviderType,
    ServiceResult
} from "@/lib/types";
import {ClientSession} from "mongoose";
import inquiryRepository from "@/features/inquiry/inquiry.repository";
import {InternalServerError} from "@/lib/error";
import {handleInquiryServiceError} from "@/utils/helpers-server";
import JibitFetchClient from "@/features/jibit/jibit.fetchClient";

type CardToDepositInput = {
    cardNumber: string,
} & BaseParamsType

class CardToDepositService implements IInquiryService<CardToDepositInput, ServiceResult | undefined> {

    async lookup({
                     cardNumber,
                     validPeriod,
                     userId,
                     source,
                     phone,
                     balance,
                     balance_after,
                 }: CardToDepositInput, session?: ClientSession | null): Promise<GenericResponse<any, undefined>> {
        try {
            const resultFromDb = await inquiryRepository.findOneJibitCardToDeposit({
                cardNumber,
                source,
                requireValidExpireTime: false
            }, session);

            const createBaseParams = {
                cardNumber,
                provider: ProviderType.JIBIT,
                user: userId,
                phone,
                source,
                inquiryType: InquiryTypeEnum.Jibit_CardToDeposit,
                balance,
            }

            const foundSuccessResult = resultFromDb && resultFromDb.inquiryResult;
            let response: BankInquiryType | null = null;
            if (!foundSuccessResult) {
                const jibitFetchClient = await JibitFetchClient.create()
                try {
                    response = await jibitFetchClient.convertCardToDeposit(cardNumber);
                } catch (error: any) {
                    const errorResult = error?.errors;
                    await inquiryRepository.create<JibitFailedResultType>({
                        ...createBaseParams,
                        inquiryStatus: InquiryStatus.FAILED,
                        inquiryResult: errorResult,
                        paid: false,
                    }, session)
                    return {
                        success: 'FAILED',
                        message: error?.message
                    }
                }

            }

            if (!resultFromDb && !response) {
                throw new InternalServerError();
            }

            const savedResult = await inquiryRepository.create<BankInquiryType>({
                ...createBaseParams,
                validPeriod,
                inquiryStatus: InquiryStatus.SUCCESS,
                inquiryResult: foundSuccessResult ? (resultFromDb.inquiryResult as BankInquiryType) : response!,
                paid: true,
                balance_after
            }, session)

            return {
                success: 'SUCCESS',
                data: {
                    id: savedResult!._id.toString(),
                    trackingNumber: savedResult!.trackingNumber!
                },
            };

        } catch (error: any) {
            return handleInquiryServiceError(error);
        }
    }

}

export default CardToDepositService