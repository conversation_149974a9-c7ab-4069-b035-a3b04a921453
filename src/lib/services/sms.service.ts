import Fetch<PERSON><PERSON> from "@/lib/fetch-api";
import envConfig from "@/lib/config.env";
import {SMSResponseType} from "@/lib/types";
import {InternalServerError} from "@/lib/error";

class SmsService {
    fetchInstance: FetchApi
    userName: string
    password: string

    constructor() {
        this.fetchInstance = new FetchApi();
        const evn = envConfig()

        // if (!evn.MELI_PAYAMAK.USERNAME || !evn.MELI_PAYAMAK.PASSWORD || !evn.MELI_PAYAMAK.BODY_ID) {
        //     throw new InternalServerError("MELI_PAYAMAK.USERNAME یا MELI_PAYAMAK.PASSWORD یا BODY_ID  خالی است.")
        // }

        this.userName = evn.MELI_PAYAMAK.USERNAME
        this.password = evn.MELI_PAYAMAK.PASSWORD
    }

    async sendSms(text: string, to: string, bodyId: string, retry = 3): Promise<boolean> {

        if (!bodyId) {
            throw new InternalServerError("bodyId نمی تواند خالی باشد")
        }

        for (let attempt = 1; attempt <= retry; attempt++) {
            try {
                const responseResult = await this.fetchInstance.post<SMSResponseType>(
                    "https://rest.payamak-panel.com/api/SendSMS/BaseServiceNumber",
                    {
                        username: this.userName,
                        password: this.password,
                        bodyId,
                        text: text,
                        to,
                    }
                );

                if (responseResult.StrRetStatus === "Ok") {
                    return true;
                } else {
                    console.warn(`تلاش ${attempt}: وضعیت دریافتی از سرور "${responseResult.StrRetStatus}" بود`);
                }
            } catch (error: any) {
                console.error(`تلاش ${attempt} با خطا مواجه شد:`, error?.message || error);
                if (attempt < retry) {
                    // صبر کردن به مدت ۱ ثانیه قبل از تلاش مجدد
                    await new Promise((resolve) => setTimeout(resolve, 1000));
                }
            }
        }

        return false;
    }

}

export default new SmsService()