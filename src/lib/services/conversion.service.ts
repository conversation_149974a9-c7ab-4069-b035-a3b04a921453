import Fetch<PERSON><PERSON> from "@/lib/fetch-api";
import envConfig from "@/lib/config.env";
import {CONVERSION_X_API_KEY} from "@/lib/constants";

type ConversionResponse = {
    ok: boolean;
    error?: string;
    message?: string;
}

class ConversionService {
    private fetchInstance: FetchApi | null = null;

    constructor() {
        const env = envConfig()
        const baseUrl = env.CONVERSION.BASEURL
        const secret = env.CONVERSION.API_SECRET
        this.fetchInstance = new FetchApi(baseUrl);
        this.fetchInstance.setHeader(CONVERSION_X_API_KEY, secret);
    }

    async sendPurchaseConversion(data: {
        gclid?: string,
        clientId: string,
        source: string,
        mobile: string,
        value: number,
        transactionId: string,
        ip?: string,
        agent?: string
    }): Promise<{ success: boolean, message?: string }> {
        try {
            const conversionResult = await this.fetchInstance!.post<ConversionResponse>('/api/v1/purchase-conversion', data);
            if (!conversionResult) {
                return {
                    success: false
                }
            }
            console.log(conversionResult)
            return {
                success: conversionResult.ok,
                message: conversionResult?.message || conversionResult?.error
            }
        } catch (error: any) {
            console.error(error)
            return {
                success: false,
            }
        }
    }
}

export default new ConversionService()