import {
    BankInquiryType,
    BaseParamsType,
    GenericResponse,
    IInquiryService, InquiryStatus, InquiryTypeEnum,
    ProviderType,
    ServiceResult
} from "@/lib/types";
import {ClientSession} from "mongoose";
import inquiryRepository from "@/features/inquiry/inquiry.repository";
import {InternalServerError} from "@/lib/error";
import {handleInquiryServiceError} from "@/utils/helpers-server";
import JibitFetchClient from "@/features/jibit/jibit.fetchClient";

type CardToIbanInput = {
    cardNumber: string,
} & BaseParamsType

class CardToIbanService implements IInquiryService<CardToIbanInput, ServiceResult | undefined> {

    async lookup({
                     cardNumber,
                     validPeriod,
                     userId,
                     source,
                     phone,
                     balance,
                     balance_after
                 }: CardToIbanInput, session?: ClientSession | null): Promise<GenericResponse<any, undefined>> {

        const createBaseParams = {
            cardNumber,
            provider: ProviderType.JIBIT,
            user: userId,
            source,
            phone,
            inquiryType: InquiryTypeEnum.Jibit_CardToIban,
            balance,
        }

        try {
            const resultFromDb = await inquiryRepository.findOneJibitCardToIban({
                cardNumber,
                requireValidExpireTime: false,
                source,
            }, session);
            const foundSuccessResult = resultFromDb && resultFromDb.inquiryResult;
            let response: BankInquiryType | null = null;
            if (!foundSuccessResult) {
                const jibitFetchClient = await JibitFetchClient.create()
                try {
                    response = await jibitFetchClient.convertCardToIban(cardNumber)
                } catch (error: any) {
                    console.log(error)
                    const errorResult = error?.errors;
                    await inquiryRepository.create<BankInquiryType>({
                        ...createBaseParams,
                        inquiryStatus: InquiryStatus.FAILED,
                        inquiryResult: errorResult,
                        paid: false,
                    }, session)
                    return {
                        success: 'FAILED',
                        message: error?.message
                    }
                }
            }

            if (!resultFromDb && !response) {
                throw new InternalServerError();
            }

            const savedResult = await inquiryRepository.create<BankInquiryType>({
                ...createBaseParams,
                validPeriod,
                inquiryStatus: InquiryStatus.SUCCESS,
                inquiryResult: foundSuccessResult ? (resultFromDb.inquiryResult as BankInquiryType) : response!,
                paid: true,
                balance_after
            }, session)

            return {
                success: 'SUCCESS',
                data: {
                    id: savedResult!._id.toString(),
                    trackingNumber: savedResult!.trackingNumber!
                },
            };
        } catch (error: any) {
            return handleInquiryServiceError(error);
        }
    }

}

export default CardToIbanService