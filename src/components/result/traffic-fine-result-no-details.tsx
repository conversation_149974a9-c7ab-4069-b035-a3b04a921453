'use client'

import {cn} from "@/lib/utils";
import {VehiclePaymentDetails} from "@/lib/types";
import ChoiceWrapper from "@/components/common/choice-wrapper";

type Props = {
    inquiryDetails: VehiclePaymentDetails
}


export default function TrafficFineResultNoDetails({inquiryDetails}: Props) {
    return (
        <ChoiceWrapper
            backgroundColor='#F9FAFB'
            borderColor="#CCCACA"
        >
            <div className='relative  w-full'>
                <div className={cn('w-full p-2 pt-5 pb-5 overflow-hidden duration-200 transition-all')}>

                    {/*<div className='w-full flex justify-between items-center'>*/}
                    {/*    <span></span>*/}
                    {/*    <CameraIcon height={40} width={40}/>*/}
                    {/*</div>*/}

                    <div className={cn('mt-5 flex flex-col items-center gap-5')}>
                        <div className='w-full flex justify-between items-center text-[#596068]'>
                            <span className='text-xs'>شناسه قبض:</span>
                            <span className='text-xs'>{inquiryDetails.BillID}</span>
                        </div>
                        <div className='w-full flex justify-between items-center text-[#596068]'>
                            <span className='text-xs'>شناسه پرداخت:</span>
                            <span className='text-xs'>{inquiryDetails.PaymentID}</span>
                        </div>
                        <div className='w-full flex justify-between items-center text-[#596068]'>
                            <span className='text-xs'>کد شکایت:</span>
                            <span className='text-xs'>{inquiryDetails.ComplaintCode || '-'}</span>
                        </div>
                        <div className='w-full flex justify-between items-center text-[#596068]'>
                            <span className='text-xs'>وضعیت شکایت:</span>
                            <span className='text-xs'>{inquiryDetails.ComplaintStatus || '-'}</span>
                        </div>
                    </div>

                </div>

            </div>
        </ChoiceWrapper>
    );
}
