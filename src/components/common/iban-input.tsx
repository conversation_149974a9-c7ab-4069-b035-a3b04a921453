import {useState} from "react";

type Props = {
    value?: string;
    onChange?: (value: string) => void;
}

const pre = "IR"
export default function IbanInput({value, onChange}: Props) {
    const [digits, setDigits] = useState("");


    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        let value = e.target.value.replace(/\D/g, ""); // فقط اعداد
        if (value.length > 24) {
            value = value.slice(0, 24);
        }
        setDigits(value);
        onChange?.(pre + value);
    };

    const formatDigits = (value: string) => {
        // هر 4 عدد یک فاصله
        return value.replace(/(.{4})/g, "$1 ").trim();
    };

    return (
        <div className="w-full flex flex-col gap-2">
            <div
                className="flex flex-row-reverse items-center border rounded-md focus-within:border-2 focus-within:border-neutral-300 py-1 px-1 md:px-4">
                <span className="font-bold mr-2">{pre}</span>
                <div className="h-6 w-px bg-gray-300 mx-1 md:mx-2"/>
                <input
                    id="iban"
                    type="text"
                    autoFocus
                    inputMode="numeric"
                    value={formatDigits(digits)}
                    onChange={handleChange}
                    className="flex-1 left-direction rounded-md h-[45px] md:h-[50px] text-center text-neutral-700 outline-none text-xs md:text-lg tracking-widest "
                    placeholder="---- ---- ---- ---- ---- ----"
                />
            </div>
        </div>
    );
}