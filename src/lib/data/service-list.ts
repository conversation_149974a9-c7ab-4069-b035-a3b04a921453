import {
    CategorySlugEnum,
    InquiryPolicyEnum,
    InquiryTypeEnum,
    ProviderType,
    ServiceType,
    SettingsValueTypeEnum
} from "@/lib/types";

export const serviceList: ServiceType[] = [
    {
        key: InquiryTypeEnum.Jibit_DepositToIban,
        provider: ProviderType.JIBIT,
        value: SettingsValueTypeEnum.DEACTIVE,
        title: "حساب به شبا",
        price: "3500",
        category: CategorySlugEnum.BANKING_CONVERSION_INQUIRY,
        inquiryPolicy: InquiryPolicyEnum.Create_new,
        validPeriod: 1,
        env: 'production'
    },
    {
        key: InquiryTypeEnum.<PERSON><PERSON><PERSON>o_DepositToIban,
        provider: ProviderType.GHABZINO,
        value: SettingsValueTypeEnum.ACTIVE,
        title: "حساب به شبا",
        price: "3500",
        category: CategorySlugEnum.BANKING_CONVERSION_INQUIRY,
        inquiryPolicy: InquiryPolicyEnum.Create_new,
        validPeriod: 1,
        env: 'production'
    },
    {
        key: InquiryTypeEnum.Ghanzino_CardToIban,
        provider: ProviderType.GHABZINO,
        value: SettingsValueTypeEnum.ACTIVE,
        title: "کارت به شبا",
        price: "3500",
        category: CategorySlugEnum.BANKING_CONVERSION_INQUIRY,
        inquiryPolicy: InquiryPolicyEnum.Create_new,
        validPeriod: 1,
        env: 'development'
    },
    {
        key: InquiryTypeEnum.Jibit_CardToDeposit,
        provider: ProviderType.JIBIT,
        value: SettingsValueTypeEnum.ACTIVE,
        title: "کارت به حساب",
        price: "3500",
        inquiryPolicy: InquiryPolicyEnum.Create_new,
        validPeriod: 1,
        category: CategorySlugEnum.BANKING_CONVERSION_INQUIRY,
        env: 'production'
    },
    {
        key: InquiryTypeEnum.Jibit_CardToIban,
        provider: ProviderType.JIBIT,
        value: SettingsValueTypeEnum.ACTIVE,
        title: "کارت به شبا",
        price: "3500",
        inquiryPolicy: InquiryPolicyEnum.Create_new,
        validPeriod: 1,
        category: CategorySlugEnum.BANKING_CONVERSION_INQUIRY,
        env: 'production'
    },
    {
        key: InquiryTypeEnum.Ghabzino_SayadCheckInquiry,
        provider: ProviderType.GHABZINO,
        value: SettingsValueTypeEnum.ACTIVE,
        title: "چک صیادی",
        price: "6700",
        inquiryPolicy: InquiryPolicyEnum.Create_new,
        validPeriod: 1,
        category: CategorySlugEnum.SAYADI_CHECK_INQUIRY,
        env: 'production'
    },
    {
        key: InquiryTypeEnum.Jibit_IbanInquiry,
        provider: ProviderType.JIBIT,
        value: SettingsValueTypeEnum.ACTIVE,
        title: "استعلام شبا",
        price: "5200",
        inquiryPolicy: InquiryPolicyEnum.Create_new,
        validPeriod: 1,
        category: CategorySlugEnum.BANKING_INQUIRY,
        env: 'production'
    },
    {
        key: InquiryTypeEnum.Jibit_CardInquiry,
        provider: ProviderType.JIBIT,
        value: SettingsValueTypeEnum.ACTIVE,
        title: "استعلام کارت",
        price: "5200",
        inquiryPolicy: InquiryPolicyEnum.Create_new,
        validPeriod: 1,
        category: CategorySlugEnum.BANKING_INQUIRY,
        env: 'production'
    },
    {
        key: InquiryTypeEnum.Ghanzino_KhalafiKhodro,
        provider: ProviderType.GHABZINO,
        value: SettingsValueTypeEnum.DEACTIVE,
        title: "خلافی خودرو",
        price: "16200",
        inquiryPolicy: InquiryPolicyEnum.Show_Last_valid,
        validPeriod: 24,
        category: CategorySlugEnum.KHALAFI_INQUIRY,
        env: 'production'
    },
    {
        key: InquiryTypeEnum.Ghanzino_KhalafiMotor,
        provider: ProviderType.GHABZINO,
        value: SettingsValueTypeEnum.DEACTIVE,
        title: "خلافی موتور",
        price: "16200",
        inquiryPolicy: InquiryPolicyEnum.Show_Last_valid,
        validPeriod: 24,
        category: CategorySlugEnum.KHALAFI_INQUIRY,
        env: 'production'
    },
]