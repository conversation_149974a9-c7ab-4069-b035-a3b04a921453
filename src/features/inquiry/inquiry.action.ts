'use server'

import {
    ActionResult,
    BaseParamsType,
    GhabzinoResponse,
    GhabzinoResultType,
    InquireResponse,
    InquiryPolicyEnum,
    InquiryType,
    InquiryTypeEnum,
    JibitResultType,
    SettingsValueTypeEnum,
    TransactionType,
    VehiclePaymentDetails,
} from "@/lib/types";
import {connectToDatabase} from "@/lib/mongodb";
import {
    getAppSource,
    getCurrentUser,
    handleActionErrorResponse,
    handleBalanceOperation,
    handleInquiryValidation,
    miladiToShamsi,
} from "@/utils/helpers-server";
import {
    ForbiddenError,
    GoneError,
    InternalServerError,
    NotFoundError,
    ServiceUnavailableError,
    UnauthorizedError
} from "@/lib/error";
import mongoose, {Types} from "mongoose";
import {convertInquiryTypes} from "@/utils/convert-types";
import {INQUIRY_PATH, INQUIRY_TYPE, IS_NEW, TRACKING_NUMBER_EXPIRE_TIME_IN_HOUR} from "@/lib/constants";
import serviceRepository from "@/features/service/service.repository";
import userRepository from "@/features/user/user.repository";
import inquiryRepository from "@/features/inquiry/inquiry.repository";
import {INQUIRY_RESULT_PATH} from "@/lib/routes";
import categoryRepository from "@/features/category/category.repository";
import handleInquiryValues, {
    handleGetTrafficInquiry,
} from "@/features/inquiry/helpers/inquiry-helpers";
import {handleInquiryLookup} from "@/features/inquiry/helpers/inquiry-lookup";
import {getLastValidInquiry} from "@/features/inquiry/helpers/get-last-valid-inquiry";

export async function inquireByServiceType(params: InquiryType): Promise<ActionResult<InquireResponse>> {
    await connectToDatabase()
    const session = await mongoose.startSession();
    session.startTransaction();
    try {
        const {
            inquiryType
        } = params;
        const isNewInquiry = params?.newInquiry === 'true'

        const currentUser = await getCurrentUser();
        if (!currentUser) {
            throw new UnauthorizedError()
        }

        //check if values of inquiry params are valid
        handleInquiryValidation(params)

        handleInquiryValues(params)

        const source = await getAppSource()

        const userFromDb = await userRepository.findUserById({source, id: currentUser.sub!}, session);

        if (!userFromDb) {
            console.error(` کاربری با شناسه ${currentUser.sub} وجود ندارد`);
            throw new NotFoundError('کاربر یافت نشد')
        }

        if (userFromDb.lock === 1) {
            throw new ForbiddenError("حساب شما قفل شده")
        }

        const serviceFromDb = await serviceRepository.findByKey(inquiryType, session)

        if (!serviceFromDb) {
            const message = ` یافت نشد ${inquiryType} سرویس`
            throw new NotFoundError(message)
        }

        // service is disabled by admin
        if (serviceFromDb.value === SettingsValueTypeEnum.DEACTIVE) {
            throw new ServiceUnavailableError()
        }

        let baseParams: BaseParamsType = {
            userId: userFromDb._id.toString(),
            phone: userFromDb.phone,
            source,
            // inquiry will expire after this period
            validPeriod: serviceFromDb.validPeriod ?? TRACKING_NUMBER_EXPIRE_TIME_IN_HOUR,
        };

        // check if inquiry should fetch from previous results
        if (serviceFromDb.inquiryPolicy === InquiryPolicyEnum.Show_Last_valid &&
            !isNewInquiry) {
            // if inquiry results not already expired they fetch here
            const inquiryResultFromDb = await getLastValidInquiry({phone: baseParams.phone, source, params}, session);
            if (inquiryResultFromDb) {
                await session.commitTransaction();
                return {
                    success: true,
                    data: {
                        trackingNumber: inquiryResultFromDb.trackingNumber,
                        isNew: false,
                    }
                }
            }
        }

        const inquiryPrice = serviceFromDb.price;

        if (isNaN(inquiryPrice) && inquiryPrice > 0) {
            throw new InternalServerError("قیمت سرویس نامعتبر است.");
        }

        const {walletResult} = await handleBalanceOperation({
            ...baseParams,
            reason: `بابت استعلام ${inquiryType}`,
            amount: -inquiryPrice
        }, session);

        if (!walletResult) {
            throw new InternalServerError("استعلام ناموفق");
        }

        baseParams = {
            ...baseParams,
            balance: walletResult.balance_before,
            balance_after: walletResult.balance_after,
        }

        const inquiryResult = await handleInquiryLookup(params, baseParams, session);

        const shouldPayBack = !inquiryResult ||
            (inquiryResult && inquiryResult.success === 'FAILED') ||
            ((inquiryResult && (inquiryResult.success === 'SUCCESS' || inquiryResult.success === 'PENDING')) && inquiryResult?.alreadyPaid)

        if (shouldPayBack) {
            await handleBalanceOperation({
                ...baseParams,
                amount: inquiryPrice,
                reason: `برگشت پول به کیف کاربر در استعلام ناموفق${inquiryType}`,
                refundId: walletResult._id.toString(),
                referenceId: inquiryResult?.data?.id,
                referenceType: inquiryResult?.data ? TransactionType.INQUIRY : undefined,
            }, session);

            const message = inquiryResult?.message || 'استعلام ناموفق بود'
            await session.commitTransaction();
            return {
                success: false,
                message,
            }
        }

        walletResult!.reference_type = TransactionType.INQUIRY
        walletResult!.reference_id = inquiryResult?.data!.id.toString()
        await walletResult?.save({session})

        await session.commitTransaction();
        console.log("استعلام با موفقیت انجام شد");
        return {
            success: true,
            data: {
                trackingNumber: inquiryResult.data!.trackingNumber!,
                isNew: true,
            },
        }
    } catch (error: any) {
        await session.abortTransaction();
        return handleActionErrorResponse(error)
    } finally {
        await session.endSession();
    }
}

export async function getInquiryByTrackingNumber(trackingNumber: string): Promise<ActionResult<JibitResultType | GhabzinoResultType | Partial<VehiclePaymentDetails>>> {
    try {
        await connectToDatabase()
        const currentUser = await getCurrentUser();

        if (!currentUser) {
            throw new UnauthorizedError()
        }

        const source = await getAppSource()

        const inquiryFromDb = await inquiryRepository.findByTrackingNumber({trackingNumber, source});

        if (!inquiryFromDb) {
            throw new NotFoundError()
        }

        if ((inquiryFromDb.user as Types.ObjectId).toString() !== currentUser.sub!) {
            throw new ForbiddenError()
        }

        const currentTime = new Date()

        if (inquiryFromDb.trackingNumberExpireTime! < currentTime) {
            throw new GoneError('مدت اعتبار کد رهگیری استعلام شما به پایان رسیده است. لطفاً یک استعلام جدید ثبت نمایید.')
        }

        const isTrafficFinesInquiry =
            inquiryFromDb.inquiryType === InquiryTypeEnum.Ghanzino_KhalafiMotor ||
            inquiryFromDb.inquiryType === InquiryTypeEnum.Ghanzino_KhalafiKhodro;

        if (isTrafficFinesInquiry) {
            await handleGetTrafficInquiry(inquiryFromDb)
        }

        return {
            success: true,
            data: convertInquiryTypes(inquiryFromDb),
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}

export async function getInquiriesForHistory(): Promise<ActionResult<(JibitResultType | GhabzinoResultType | Partial<VehiclePaymentDetails> | undefined)[]>> {
    try {
        await connectToDatabase()
        const currentUser = await getCurrentUser();

        if (!currentUser) {
            throw new UnauthorizedError()
        }

        const source = await getAppSource()

        const recentInquiriesFromDb = await inquiryRepository.findInquiriesByUserIdIfNotExpired({
            userId: currentUser.sub!,
            source
        })

        console.log(recentInquiriesFromDb)
        const inquiriesWithUrls = await Promise.all(recentInquiriesFromDb.map(async (inquiry) => {
            inquiry.inquiryResult.dateInquiry = miladiToShamsi(inquiry.createdAt!.toString())
            if (inquiry.inquiryType === InquiryTypeEnum.Ghanzino_KhalafiMotor ||
                inquiry.inquiryType === InquiryTypeEnum.Ghanzino_KhalafiKhodro) {
                let inquiryPath = '';

                const service = await serviceRepository.findByKey(inquiry.inquiryType);
                if (service) {
                    const category = await categoryRepository.findBySlug(service.category);
                    if (category) {
                        inquiryPath = category.path;
                    }
                }

                const inquiryResult = inquiry.inquiryResult as GhabzinoResponse<VehiclePaymentDetails>
                const parameters = inquiryResult.Parameters
                if (parameters) {
                    const amount = parameters.Amount
                    const totalAmount = parameters.TotalAmount
                    if (amount) {
                        inquiryResult.Parameters.Amount = amount / 10
                    }
                    if (totalAmount) {
                        inquiryResult.Parameters.TotalAmount = totalAmount / 10
                    }
                }

                const inquiryUrl = `${INQUIRY_RESULT_PATH}${inquiry.trackingNumber}?${INQUIRY_TYPE}=${inquiry.inquiryType}&${IS_NEW}=false&${INQUIRY_PATH}=${inquiryPath}`;
                inquiry.inquiryUrl = inquiryUrl;
            }

            return convertInquiryTypes(inquiry, true);
        }));


        return {
            success: true,
            data: inquiriesWithUrls,
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}