import {CheckCircle2, CreditCard, XCircle} from "lucide-react";
import {formatWithComma} from "@/utils/helpers";
import {verifyPayment} from "@/features/transaction/transaction.action";
import {InquiryStatusPaymentResult, PaymentStatusResult} from "@/lib/types";
import {Suspense} from "react";
import InquiryResultContents from "@/components/depo/result/inquiry-result-contents";
import Loading from "@/app/loading";

type Props = {
    Authority: string;
    Status: string;
}

export async function PaymentResultContents({Status, Authority}: Props) {

    const paymentActionResult = await verifyPayment(Authority, Status);
    const paymentStatus: PaymentStatusResult = paymentActionResult.success ? 'success' : 'failed';
    const paymentDetails = paymentActionResult?.data
    const paymentMessage = paymentActionResult?.message
    const inquiryStatus: InquiryStatusPaymentResult = (paymentActionResult.success && paymentActionResult.data?.hasInquiry) ? 'pending' : 'no-query';

    return (
        <div className="p-6">
            <div
                className={`mb-4 rounded-lg border p-4 ${paymentStatus === "failed" ? "border-red-200 " : ""}`}>
                <div className="mb-3 flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <CreditCard className="h-5 w-5 text-slate-600"/>
                        <h2 className="text-lg font-medium">وضعیت پرداخت</h2>
                    </div>
                    <div
                        className={`flex items-center gap-1 rounded-full px-3 py-1 text-sm font-medium ${
                            paymentStatus === "success" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                        }`}
                    >
                        {paymentStatus === "success" ? (
                            <>
                                <CheckCircle2 className="h-4 w-4"/>
                                <span>موفق</span>
                            </>
                        ) : (
                            <>
                                <XCircle className="h-4 w-4"/>
                                <span>ناموفق</span>
                            </>
                        )}
                    </div>
                </div>

                <p className="text-sm text-slate-600">
                    {
                        paymentMessage
                    }
                </p>

                {paymentStatus === "success" && (
                    <>
                        {paymentDetails?.amount && <div className="mt-3 text-xs text-slate-500">
                            <span>مبلغ: </span>
                            <span className="font-mono" dir="ltr">
                                              {formatWithComma(paymentDetails.amount.toString())}
                                            </span>
                            <span className='mx-1 font-mono'>تومان</span>
                        </div>}
                        <div className="mt-3 text-xs text-slate-500">
                            <span>شماره پیگیری: </span>
                            <span className="font-mono" dir="ltr">
                                              {paymentDetails?.ref_id}
                                            </span>
                        </div>
                    </>
                )}
            </div>

            {
                <Suspense
                    fallback={
                        <div
                            className='w-full p-6 h-[200px] border border-gray-200 rounded-b-md'>
                            <Loading
                                fullHeight={false}/>
                        </div>}>
                    <InquiryResultContents
                        paymentStatus={paymentStatus}
                        inquiryStatus={inquiryStatus}
                        inquiry={paymentDetails?.inquiry}
                    />
                </Suspense>
            }
        </div>
    );
}
