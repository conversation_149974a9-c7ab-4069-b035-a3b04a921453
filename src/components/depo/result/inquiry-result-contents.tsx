import {CheckCircle2, Search, XCircle} from "lucide-react";
import PaymentButtonActions from "@/components/depo/result/payment-button-actions";
import {ActionResult, InquireResponse, InquiryStatusPaymentResult, InquiryType, PaymentStatusResult} from "@/lib/types";
import {inquireByServiceType} from "@/features/inquiry/inquiry.action";
import {INQUIRY_RESULT_PATH} from "@/lib/routes";
import DisplayCounter from "@/components/depo/result/display-counter";
import {INQUIRY_PATH, INQUIRY_TYPE, IS_NEW} from "@/lib/constants";

type Props = {
    inquiryStatus: InquiryStatusPaymentResult,
    paymentStatus: PaymentStatusResult
    inquiry?: InquiryType
}

export default async function InquiryResultContents({paymentStatus, inquiryStatus, inquiry}: Props) {

    let inquiryActionResult: ActionResult<InquireResponse> | undefined;
    let inquiryMessage: string | undefined;
    let newInquiryUrl: string | undefined;
    let inquiryResultUrl: string | undefined;
    if (inquiryStatus === 'pending') {
        const {inquiryPath, inquiryType} = inquiry!;
        newInquiryUrl = (inquiryPath && inquiryType) ? `${inquiryPath}/?selected=${inquiryType}` : undefined
        inquiryActionResult = await inquireByServiceType({...inquiry!, newInquiry: 'true'});
        inquiryMessage = inquiryActionResult?.message;
        inquiryStatus = inquiryActionResult.success ? 'success' : 'failed';
        const inquiryDetails = inquiryActionResult?.data
        if (inquiryDetails) {
            inquiryResultUrl = inquiryStatus === 'success' ?
                `${INQUIRY_RESULT_PATH}${inquiryDetails.trackingNumber}?${INQUIRY_TYPE}=${inquiry?.inquiryType}&${INQUIRY_PATH}=${inquiry?.inquiryPath ?? ''}&${IS_NEW}=${inquiryDetails.isNew ? 'true' : 'false'}` :
                '';
        }
    }
    return (
        <>
            {(inquiryStatus === 'success' || inquiryStatus === 'failed') && (
                <div className="mb-6 rounded-lg border p-4">
                    <div className="mb-3 flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            <Search className="h-5 w-5 text-slate-600"/>
                            <h2 className="text-lg font-medium">وضعیت استعلام</h2>
                        </div>
                        <div
                            className={`flex items-center gap-1 rounded-full px-3 py-1 text-sm font-medium ${
                                inquiryStatus === "success" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                            }`}
                        >
                            {inquiryStatus === "success" ? (
                                <>
                                    <CheckCircle2 className="h-4 w-4"/>
                                    <span>موفق</span>
                                </>
                            ) : (
                                <>
                                    <XCircle className="h-4 w-4"/>
                                    <span>ناموفق</span>
                                </>
                            )}
                        </div>
                    </div>

                    <p className="text-sm text-slate-600">
                        {inquiryStatus === "success"
                            ? "استعلام شما با موفقیت انجام شد و اطلاعات مورد نظر دریافت گردید."
                            : (inquiryMessage || "متأسفانه استعلام شما با مشکل مواجه شد. این می‌تواند به دلیل مشکلات ارتباطی یا اطلاعات نادرست باشد.")}
                    </p>

                    {(inquiryStatus === 'success' && inquiryResultUrl) &&
                        <DisplayCounter inquiryUrl={inquiryResultUrl}/>}
                </div>)
            }
            <PaymentButtonActions
                newInquiryUrl={newInquiryUrl}
                inquiryResultUrl={inquiryResultUrl}
                paymentStatus={paymentStatus}
                inquiryStatus={inquiryStatus}
            />
        </>
    );
}
