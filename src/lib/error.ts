import status from "http-status";

export abstract class HttpError extends Error {
    statusCode: number;

    constructor(message: string, statusCode: number) {
        super(message);
        this.statusCode = statusCode;
        this.name = this.constructor.name;
        Error.captureStackTrace(this, this.constructor);
    }
}

export class BadRequestError extends HttpError {
    constructor(message: string = 'درخواست اشتباه') {
        super(message, status.BAD_REQUEST);
    }
}

export class NotFoundError extends HttpError {
    constructor(message: string = 'یافت نشد') {
        super(message, status.NOT_FOUND);
    }
}

export class ServiceUnavailableError extends HttpError {
    constructor(message: string = 'سرویس در دسترس نیست') {
        super(message, status.SERVICE_UNAVAILABLE);
    }
}

export class UnauthorizedError extends HttpError {
    constructor(message: string = 'دسترسی غیرمجاز! لطفاً ابتدا وارد حساب کاربری خود شوید.') {
        super(message, status.UNAUTHORIZED);
    }
}

export class ForbiddenError extends HttpError {
    constructor(message: string = 'دسترسی غیرمجاز! شما اجازه مشاهده این بخش را ندارید.') {
        super(message, status.FORBIDDEN);
    }
}

export class GoneError extends HttpError {
    constructor(message: string = 'شناسه پیگیری منقضی شده است') {
        super(message, status.GONE);
    }
}

export class InternalServerError extends HttpError {
    errors: any

    constructor(message: string = 'خطای داخلی سرور', errors?: any) {
        super(message, status.INTERNAL_SERVER_ERROR);
        this.errors = errors;
    }
}

export class ConflictError extends HttpError {
    constructor(message: string = 'یک تضاد در داده‌ها وجود دارد.') {
        super(message, status.CONFLICT);
    }
}

export class TimeoutError extends HttpError {
    constructor(message: string = 'زمان پاسخگویی به درخواست به پایان رسید.') {
        super(message, status.REQUEST_TIMEOUT); // 408
    }
}

export class ValidationError extends HttpError {
    errors: string[];

    constructor(errors: string[], message: string = 'خطای اعتبارسنجی, مقادیر ارسال شده معتبر نمیباشد') {
        super(message, status.UNPROCESSABLE_ENTITY);
        this.errors = errors;
    }
}

export class NotEnoughForPaymentError extends HttpError {
    constructor(message: string = 'مبلغ کافی برای پرداخت موجود نیست') {
        super(message, status.PAYMENT_REQUIRED); // Using HTTP status code for Payment Required
    }
}