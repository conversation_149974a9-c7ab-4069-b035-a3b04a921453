import {
    CAR_PLATE_LEFT,
    CAR_PLATE_MIDDLE, CAR_PLATE_RIGHT, carPlatePartsMaxLengths,
    MOTOR_PLATE_LEFT,
    MOTOR_PLATE_RIGHT, motorPlatePartsMaxLengths, plateAlphabets
} from "@/lib/constants";
import {PlaqueType} from "@/lib/types";

export function plateNumberIsNotValid(plateNumber: PlaqueType, isMotor: boolean) {
    const {left, middle, right, alphabet} = plateNumber;

    const leftValid = left && (isMotor ? left.length === motorPlatePartsMaxLengths[MOTOR_PLATE_LEFT] : left.length === carPlatePartsMaxLengths[CAR_PLATE_LEFT])
    const rightValid = right && (isMotor ? right.length === motorPlatePartsMaxLengths[MOTOR_PLATE_RIGHT] : right.length === carPlatePartsMaxLengths[CAR_PLATE_RIGHT])
    const middleValid = isMotor ? true : (middle && middle.length === carPlatePartsMaxLengths[CAR_PLATE_MIDDLE])
    const alphabetValid = isMotor ? true : (alphabet && plateAlphabets.includes(alphabet))

    return !(leftValid && rightValid && middleValid && alphabetValid);
}

export function isValidIranianNationalCode(code?: string) {
    if (!code) return false;
    if (!/^\d{10}$/.test(code) || /^(\d)\1{9}$/.test(code)) return false;

    const digits = [...code].map(Number);
    const checkDigit = digits[9];

    // Compute checksum
    const sum = digits.slice(0, 9).reduce((acc, digit, i) => acc + digit * (10 - i), 0);
    const remainder = sum % 11;
    const expectedCheckDigit = remainder < 2 ? remainder : 11 - remainder;

    return checkDigit === expectedCheckDigit;
}