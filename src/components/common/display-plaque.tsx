import PlateInputMotor from "@/components/common/motor-plate-input";
import PlateInputCar from "@/components/common/car-plate-input";


type Props = {
    left: string;
    right: string;
    middle: string | undefined;
    alphabet: string | undefined;
    isMotor: boolean;
}


export default function DisplayPlaque({left, middle, alphabet, right, isMotor}: Props) {
    return (
        <>
            {
                isMotor ? (
                    <PlateInputMotor readOnly value={[left, right]}/>
                ) : (<PlateInputCar readOnly value={[left, alphabet!, middle!, right]}/>)
            }
        </>
    );
}
