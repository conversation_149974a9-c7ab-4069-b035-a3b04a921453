import {z} from "zod";
import {IRAN_MOBILE_REGEX} from "@/lib/validations";
import {AccountTypeEnum, ViolationTypeEnum} from "@/lib/types";

export const iranMobileSchema = z
    .string()
    .regex(IRAN_MOBILE_REGEX, "لطفا شماره صحیح را وارد کنید")

export const loginFormSchema = z.object({
    mobile: iranMobileSchema,
    code: z.string().length(5, "کد تایید صحیح نمی باشد"),
});
export type LoginFormType = z.infer<typeof loginFormSchema>;

export const cardNumberFormSchema = z
    .array(z.string().length(4, "هر بخش شماره کارت باید دقیقا ۴ رقم باشد")
        .regex(/^\d{4}$/, "فقط اعداد مجاز هستند"))
    .length(4, "شماره کارت باید شامل ۴ بخش باشد")
    .refine(
        (val) => val.join("").length === 16,
        "شماره کارت باید دقیقا ۱۶ رقم باشد"
    )

export const GhabzinoAccountTypeSchema = z
    .nativeEnum(AccountTypeEnum, {
        errorMap: () => ({
            message: 'انتخاب نوع حساب اجباری است'
        })
    })

export const iranBankCardArraySchema = z.object({
    cardNumber: cardNumberFormSchema,
});


export const ghabzinoCardToIbanFormSchema = z.object({
    cardNumber: cardNumberFormSchema,
    accountTypeName: GhabzinoAccountTypeSchema
})

export type GhabzinoCardToIbanType = z.infer<typeof ghabzinoCardToIbanFormSchema>;

export const iranBankCardNumberSchema = z.string().regex(/^[0-9]{16}$/, {
    message: "شماره کارت بانکی ایرانی باید دقیقاً 16 رقم باشد.",
});

export const GhabzinoCardToIbanServerSchema = z.object({
    cardNumber: iranBankCardNumberSchema,
    accountTypeName: GhabzinoAccountTypeSchema
})


export type IranBankCardType = z.infer<typeof iranBankCardArraySchema>;

export const jibitAccountToIbanSchema = z.object({
    depositNumber: z
        .string()
        .min(10, 'شماره حساب باید حداقل 10 رقم داشته باشد')
        .max(20, 'شماره حساب نمی‌تواند بیشتر از 20 رقم باشد'),
    bank: z
        .string().min(1, "انتخاب بانک الزامی است")
});

export type JibitAccountToIbanType = z.infer<typeof jibitAccountToIbanSchema>;

export const ghabzinoAccountToIbanSchema = z.object({
    depositNumber: z
        .string()
        .min(10, 'شماره حساب باید حداقل 10 رقم داشته باشد')
        .max(20, 'شماره حساب نمی‌تواند بیشتر از 20 رقم باشد'),
    bank: z
        .string().min(1, "انتخاب بانک الزامی است"),
    accountTypeName: GhabzinoAccountTypeSchema
});

export type GhabzinoAccountToIbanType = z.infer<typeof ghabzinoAccountToIbanSchema>;

const callbackUrlSchema = z.string().url({message: 'فرمت URL صحیح نیست'});

const amountSchema = z.number().min(1, {message: 'مقدار باید بزرگتر از 0 باشد'});

export const paymentRequestSchema = z.object({
    callbackUrl: callbackUrlSchema,
    amount: amountSchema
});

export const ServiceUpdateSchema = z.object({
    title: z
        .string()
        .min(1, "عنوان نمی‌تواند خالی باشد")
        .max(50, "عنوان نباید بیش از 50 کاراکتر باشد"),
    price: z
        .number({
            required_error: "قیمت الزامی است",
            invalid_type_error: "قیمت باید عدد باشد",
        })
        .min(0, "قیمت نمی‌تواند منفی باشد"),
    validPeriod: z
        .number({
            required_error: "مدت اعتبار الزامی است",
            invalid_type_error: "مدت اعتبار باید عدد باشد",
        })
        .min(1, "مدت اعتبار باید بیشتر از صفر باشد"),
});


export const sayadiNumberSchema = z.object({
    sayadId: z.string()
        .regex(/^\d{16}$/, 'شناسه صیادی باید ۱۶ رقم عددی باشد')
})


export type SayadiNumberType = z.infer<typeof sayadiNumberSchema>;

export const iranIbanSchema = z
    .string()
    .trim()
    .toUpperCase()
    .refine((val) => /^IR\d{24}$/.test(val), {
        message: "شماره شبا باید 24 رقم باشد.",
    });

export const IbanInquirySchema = z.object({
    iban: iranIbanSchema
})

export type IbanInquiryType = z.infer<typeof IbanInquirySchema>;

export const violationInquiryFormSchema = z.object({
    plateNumber: z
        .array(z.string()),
    type: z.enum([ViolationTypeEnum.WITH_INFO, ViolationTypeEnum.WITHOUT_INFO]), // Type must be one of the two allowed values.
    nationalCode: z.string().optional(),
    phoneNumber: z.string()
        .regex(IRAN_MOBILE_REGEX, "لطفا موبایل را صحیح وارد کنید")
        .optional(),
}).superRefine((data, ctx) => {
    if (data.type === ViolationTypeEnum.WITH_INFO) {
        if (!data.nationalCode) {
            ctx.addIssue({
                path: ["nationalCode"],
                message: "کد ملی الزامی است",
                code: z.ZodIssueCode.custom,
            });
        }
        if (!data.phoneNumber) {
            ctx.addIssue({
                path: ["phoneNumber"],
                message: "شماره موبایل الزامی است",
                code: z.ZodIssueCode.custom,
            });
        }
    }
});

export type ViolationInquiryType = z.infer<typeof violationInquiryFormSchema>

export const applicationSettingsSchema = z.object({
    title: z.string().nonempty("عنوان دامنه الزامی است"),
    pattern_login: z.string()
        .regex(/^\d+$/, "الگوی پیامک باید عدد باشد")
        .nonempty("الگوی پیامک الزامی است"),
    merchent_id: z.string().optional().or(z.literal("")),
    ga_apiSecret: z.string().optional(),
    ga_measurementId: z.string().optional(),
    ga_containerId: z.string().optional(),
}).superRefine((data, ctx) => {
    const {ga_apiSecret, ga_measurementId} = data;

    const anyGASet = ga_apiSecret || ga_measurementId;
    const allGASet = ga_apiSecret && ga_measurementId;

    if (anyGASet && !allGASet) {
        if (!ga_apiSecret) {
            ctx.addIssue({
                path: ["ga_apiSecret"],
                message: "کلید مخفی گوگل آنالیتیکس الزامی است",
                code: z.ZodIssueCode.custom,
            });
        }
        if (!ga_measurementId) {
            ctx.addIssue({
                path: ["ga_measurementId"],
                message: "شناسه اندازه گیری گوگل آنالیتیکس اجباری است",
                code: z.ZodIssueCode.custom,
            });
        }
    }
});