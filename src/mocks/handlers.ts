import {http, HttpResponse} from 'msw';

export const handlers = [
    // Mock the external Gibit API endpoint for bank card conversion-query
    http.get('https://api.gibit.com/v1/cards', ({request, params, cookies}) => {
        // Mocked response based on specific cardNumber
        return HttpResponse.json(
            {
                message: 'Mocked response',
            },
            {
                status: 202,
                statusText: 'Mocked status',
            }
        );
    }),

    // Mock the DB call for inquireCardToDepositFromDb

];