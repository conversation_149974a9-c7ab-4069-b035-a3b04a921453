'use client'

import CountdownTimer from "@/components/common/count-down-timer";
import {useRouter} from "next/navigation";
import {useMemo} from "react";

type Props = {
    inquiryUrl: string;
}

export default function DisplayCounter({inquiryUrl}: Props) {
    const router = useRouter()

    function handleOnComplente() {
        if (inquiryUrl) {
            router.replace(inquiryUrl);
        }
    }

    function handleRenderer(second: number) {
        return (
            <div className=" flex flex-col justify-center items-center gap-y-1">
                <div
                    className='h-[35px] w-[35px] border border-green-500 rounded-full flex justify-center items-center'>
                    <span className='text-gray-500 text-sm'>{second}</span>
                </div>
                <p className='font-base text-sm text-gray-700'>تا انتقال به صفحه استعلام</p>
            </div>

        )
    }

    const completeComponent = useMemo(() => {
        return (
            <div className=" flex flex-col justify-center items-center gap-y-1">
                <p className='font-base text-sm text-gray-700'>در حال انتقال شما به صفحه استعلام</p>
            </div>
        )
    }, [])

    return (
        <div className="my-5 w-full flex justify-center items-center">
            <CountdownTimer
                remainingTime={5}
                onComplete={handleOnComplente}
                renderer={handleRenderer}
                onCompleteComponent={completeComponent}
            />
        </div>
    );
}
