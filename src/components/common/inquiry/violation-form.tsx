'use client'

import {useForm, useWatch} from "react-hook-form";
import {zodResolver} from "@hookform/resolvers/zod";
import {
    CAR_PLATE_ALPHABET,
    CAR_PLATE_LEFT,
    CAR_PLATE_MIDDLE,
    CAR_PLATE_RIGHT, carPlatePartsMaxLengths,
    MOTOR_PLATE_LEFT,
    MOTOR_PLATE_RIGHT,
    motorPlatePartsMaxLengths,
    NATIONAL_CODE_MAX_LENGTH,
    PHONE_NUMBER_MAX_LENGTH
} from "@/lib/constants";
import {useEffect, useRef} from "react";
import {Form, FormControl, FormField, FormItem, FormMessage} from "@/components/ui/form";
import PlateInputCar, {PlateInputRef} from "@/components/common/car-plate-input";
import PlateInputMotor from "@/components/common/motor-plate-input";
import {isValidIranianNationalCode, plateNumberIsNotValid} from "@/utils/validators";
import ChoiceItem from "@/components/common/choice-item";
import CustomInput from "@/components/common/custom-input";
import {violationInquiryFormSchema, ViolationInquiryType} from "@/lib/zod-schemas";
import {InquiryTypeEnum, ServiceComponentProps, ViolationType, ViolationTypeEnum} from "@/lib/types";
import CustomButton from "@/components/common/custom-button";
import InquiryPrice from "@/components/common/inquiry/inquiry-price";

type Props = ServiceComponentProps & {
    // queryParams: ViolationQueryParams
    isMotor?: boolean;
}

const CAR = 'خودرو';
const MOTOR_CYCLE = 'موتور سیکلت'


export function ViolationForm({isMotor, isLoading, onSubmit, price}: Props) {
    // const {left, right, middle, alphabet, withDetails, nationalCode, phoneNumber} = queryParams
    const vehicleType = isMotor ? MOTOR_CYCLE : CAR
    const nationalCodeRef = useRef<HTMLInputElement | null>(null)
    const phoneNumberRef = useRef<HTMLInputElement | null>(null)
    const plateInputRef = useRef<PlateInputRef | null>(null)


    const form = useForm<ViolationInquiryType>({
        mode: 'onSubmit',
        resolver: zodResolver(violationInquiryFormSchema),
        defaultValues: {
            plateNumber: isMotor ?
                ["", ""] :
                ["", "", "", ""],
            type: ViolationTypeEnum.WITHOUT_INFO,
            nationalCode: undefined,
            phoneNumber: undefined,
        },
    })

    const isWithInfo = form.getValues('type') === ViolationTypeEnum.WITH_INFO;
    const watchType = useWatch({control: form.control, name: 'type'})


    useEffect(() => {
        const plateNumber = form.getValues('plateNumber')
        if (watchType === ViolationTypeEnum.WITHOUT_INFO) {
            form.reset({
                type: ViolationTypeEnum.WITHOUT_INFO,
                plateNumber,
                nationalCode: undefined,
                phoneNumber: undefined,
            })
        }
        if ((!isMotor && plateNumber[CAR_PLATE_RIGHT] && plateNumber[CAR_PLATE_RIGHT].length === carPlatePartsMaxLengths[CAR_PLATE_RIGHT]) ||
            (isMotor && plateNumber[MOTOR_PLATE_RIGHT] && plateNumber[MOTOR_PLATE_RIGHT].length === motorPlatePartsMaxLengths[MOTOR_PLATE_RIGHT])) {
            return;
        }
        plateInputRef.current?.focus()
    }, [watchType])


    useEffect(() => {
        const subscription = form.watch((values) => {
            if (!isWithInfo) return;

            const plateNumber = values.plateNumber;
            const nationalCode = values.nationalCode;
            const phoneNumber = values.phoneNumber;

            if (!nationalCode && ((!isMotor && plateNumber?.[CAR_PLATE_RIGHT]?.length === carPlatePartsMaxLengths[CAR_PLATE_RIGHT]) ||
                (isMotor && plateNumber?.[MOTOR_PLATE_RIGHT]?.length === motorPlatePartsMaxLengths[MOTOR_PLATE_RIGHT])
            )) {
                nationalCodeRef.current?.focus();
            }

            if (document.activeElement === nationalCodeRef.current && !phoneNumber && nationalCode?.length === NATIONAL_CODE_MAX_LENGTH) {
                phoneNumberRef.current?.focus();
            }
        });

        return () => subscription.unsubscribe(); // Cleanup on unmount
    }, [isMotor, isWithInfo]); // `watch` reference is stable, so this won't cause extra re-renders.

    async function handleSubmit(values: ViolationInquiryType) {
        if (isLoading) return;
        const left = isMotor ? values.plateNumber[MOTOR_PLATE_LEFT] : values.plateNumber[CAR_PLATE_LEFT];
        const right = isMotor ? values.plateNumber[MOTOR_PLATE_RIGHT] : values.plateNumber[CAR_PLATE_RIGHT];
        const middle = isMotor ? undefined : values.plateNumber[CAR_PLATE_MIDDLE];
        const alphabet = isMotor ? undefined : values.plateNumber[CAR_PLATE_ALPHABET];

        if (plateNumberIsNotValid({
            left,
            right,
            middle,
            alphabet
        }, !!isMotor)) {
            form.setError('plateNumber', {message: 'شماره پلاک را صحیح وارد کنید'})
            return
        }

        const nationalId = values?.nationalCode;
        if (nationalId) {
            const isNationalCodeValid = isValidIranianNationalCode(nationalId)
            if (!isNationalCodeValid) {
                form.setError('nationalCode', {message: 'کد ملی را صحیح وارد کنید'})
                return;
            }
        }
        const inquiryType = isMotor ? InquiryTypeEnum.Ghanzino_KhalafiMotor : InquiryTypeEnum.Ghanzino_KhalafiKhodro;
        const plaque = values.plateNumber

        const newValues: ViolationType = {
            withDetails: isWithInfo ? 'true' : 'false',
            phoneNumber: isWithInfo ? values.phoneNumber : undefined,
            nationalCode: isWithInfo ? nationalId : undefined,
            middle: isMotor ? undefined : plaque[CAR_PLATE_MIDDLE],
            left: isMotor ? plaque[MOTOR_PLATE_LEFT] : plaque[CAR_PLATE_LEFT],
            right: isMotor ? plaque[MOTOR_PLATE_RIGHT] : plaque[CAR_PLATE_RIGHT],
            alphabet: isMotor ? undefined : plaque[CAR_PLATE_ALPHABET],
            isMotor: isMotor ? 'true' : 'false',
            // reInquiry: 'false'
        }

        onSubmit?.(newValues, inquiryType);

    }


    return (
        <Form {...form}>
            <form
                onSubmit={form.handleSubmit(handleSubmit)}
                className="mt-4"
                autoComplete="off"
            >

                <div className='!px-0'>
                    <div className='px-5 md:px-14'>
                        <div className='w-full mb-7'>
                            <FormField
                                control={form.control}
                                name="type"
                                render={({field}) => (
                                    <FormItem>
                                        <FormControl>
                                            <div
                                                className='w-full flex flex-col sm:flex-row items-start md:items-center  justify-normal gap-3'>
                                                <div className='flex-1 w-full'>
                                                    <ChoiceItem
                                                        label='خلافی با جزییات'
                                                        checked={ViolationTypeEnum.WITH_INFO === field.value}
                                                        value={ViolationTypeEnum.WITH_INFO}
                                                        name={field.name}
                                                        description='نیاز به کد ملی و شماره موبایل مالک'
                                                        onClick={(value => field.onChange(value))}
                                                    />
                                                </div>
                                                <div className='flex-1 w-full'>
                                                    <ChoiceItem
                                                        label='خلافی تجمیعی'
                                                        checked={ViolationTypeEnum.WITHOUT_INFO === field.value}
                                                        value={ViolationTypeEnum.WITHOUT_INFO}
                                                        name={field.name}
                                                        description='پرداخت مجموع مبلغ خلافی'
                                                        onClick={(value => field.onChange(value))}
                                                    />
                                                </div>
                                            </div>
                                        </FormControl>
                                    </FormItem>
                                )}
                            />
                        </div>
                        <div className='px-0 sm:px-7'>
                            <FormField
                                control={form.control}
                                name="plateNumber"
                                render={({field}) => (
                                    <FormItem>
                                        <FormControl>
                                            {isMotor ? <
                                                    PlateInputMotor
                                                    {...field}
                                                    ref={plateInputRef}
                                                /> :
                                                <PlateInputCar
                                                    {...field}
                                                    ref={plateInputRef}
                                                />}
                                        </FormControl>
                                        <FormMessage className='text-xs'/>
                                    </FormItem>
                                )}
                            />
                            <div className='mt-8'>
                                {
                                    form.getValues('type') === ViolationTypeEnum.WITH_INFO && (
                                        <>
                                            <div>
                                                <FormField
                                                    control={form.control}
                                                    name="nationalCode"
                                                    render={({field}) => (
                                                        <FormItem>
                                                            <p
                                                                className='text-[#596068] text-xs'>{`کد ملی مالک ${vehicleType}`}</p>
                                                            <FormControl>
                                                                <CustomInput
                                                                    allowOnlyNumbers
                                                                    maxLength={NATIONAL_CODE_MAX_LENGTH}
                                                                    placeholder='کد ملی مالک را وارد کنید'
                                                                    {...field}
                                                                    ref={nationalCodeRef}
                                                                    direction='rtl'
                                                                    inputMode='numeric'
                                                                    autoFocus
                                                                />
                                                            </FormControl>
                                                            <FormMessage className='text-xs'/>
                                                        </FormItem>
                                                    )}
                                                />
                                            </div>
                                            <div className='mt-4'>
                                                <FormField
                                                    control={form.control}
                                                    name="phoneNumber"
                                                    render={({field}) => (
                                                        <FormItem>
                                                            <p
                                                                className='text-[#596068] text-xs'>{`تلفن همراه مالک ${vehicleType}`}</p>
                                                            <FormControl>
                                                                <CustomInput
                                                                    allowOnlyNumbers
                                                                    maxLength={PHONE_NUMBER_MAX_LENGTH}
                                                                    placeholder='شماره موبایل مالک را وارد کنید'
                                                                    {...field}
                                                                    ref={phoneNumberRef}
                                                                    inputMode='numeric'
                                                                    direction='rtl'
                                                                />
                                                            </FormControl>
                                                            <FormMessage className='text-xs'/>
                                                        </FormItem>
                                                    )}
                                                />
                                            </div>
                                        </>
                                    )
                                }
                            </div>

                        </div>
                    </div>
                </div>
                <CustomButton
                    loading={isLoading}
                    disabled={isLoading}
                    variants='blue'
                    type='submit'
                    className='mt-5'
                >
                    استعلام
                </CustomButton>
                <FormMessage className='text-xs'/>
                <InquiryPrice price={price}/>
            </form>
        </Form>
    );
}
