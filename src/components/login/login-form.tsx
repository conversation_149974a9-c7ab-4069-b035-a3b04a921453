'use client'

import <PERSON><PERSON><PERSON>on from "@/components/common/custom-button";
import {<PERSON>, CardContent, CardHeader} from "@/components/ui/card";
import {Form, FormControl, FormField, FormItem, FormMessage} from "@/components/ui/form";
import {loginFormSchema, LoginFormType} from "@/lib/zod-schemas";
import {zodResolver} from "@hookform/resolvers/zod";
import {ArrowRight} from "lucide-react";
import {useRouter, useSearchParams} from "next/navigation";
import {useEffect, useState} from "react";
import {useForm} from "react-hook-form";
import toast from "react-hot-toast";
import Link from "next/link";
import CustomInput from "@/components/common/custom-input";
import CountDownTimer from "@/components/common/count-down-timer";
import {authOTP, authVerification} from "@/features/auth/auth.action";
import {useAuth} from "@/lib/hooks/useAuth";
import {RETURN_URL} from "@/lib/constants";
import {HOME_PATH} from "@/lib/routes";
import {InquiryQueryParams} from "@/lib/types";
import usePathUrlHelper from "@/lib/hooks/usePathUrlHelper";
import {InquiryInfo} from "@/components/common/InquiryInfo";
import useInquiry from "@/lib/hooks/useInquiry";
import {useApplication} from "@/lib/hooks/useApplication";

type FormStep = 1 | 2
const CODE_LENGTH = 5
export default function LoginForm() {
    const router = useRouter()
    const {application} = useApplication()
    const {reFetchUser} = useAuth()
    const {mutate, isLoading} = useInquiry()
    const [formStep, setFormStep] = useState<FormStep>(1);
    const [tempToken, setTempToken] = useState<string | undefined>()
    const searchParam = useSearchParams();
    const {getQueryParams} = usePathUrlHelper();
    const [optLoading, setOptLoading] = useState(false)
    const [enterCodeLoading, setEnterCodeLoading] = useState(false)
    const [inquiryInfo, setInquiryInfo] = useState<InquiryQueryParams | undefined>();
    const [expireTime, setExpireTime] = useState<number | undefined>()
    const form = useForm<LoginFormType>({
        resolver: zodResolver(loginFormSchema),
        defaultValues: {
            mobile: "",
            code: ""
        },
    })

    const codeLength = form.watch('code').length

    async function formSubmitOnCodeEntered() {
        const isValid = await form.trigger();
        if (isValid) {
            await form.handleSubmit(onSubmit)();

        }
    }

    useEffect(() => {
        const params = getQueryParams<InquiryQueryParams>();
        if (params?.inquiryType) {
            setInquiryInfo(params)
        }
    }, []);

    useEffect(() => {
        if (codeLength >= CODE_LENGTH) {
            formSubmitOnCodeEntered()
        }
    }, [codeLength]);

    async function onSubmit(values: LoginFormType) {
        setEnterCodeLoading(true)
        const phone = values.mobile
        const code = values.code
        const verificationResult = await authVerification({phone, code, token: tempToken!})
        if (!verificationResult.success) {
            setEnterCodeLoading(false)
            toast.error(verificationResult.message!)
            return;
        }
        reFetchUser()
        if (inquiryInfo) {
            toast.success('ورود با موفقیت انجام شد.سیستم در حال پردازش استعلام است.')
            const mutateResult = await mutate(inquiryInfo)
            if (!mutateResult.success && mutateResult.href) {
                router.replace(mutateResult.href)
            } else if (!mutateResult.success && mutateResult.message) {
                toast.error(mutateResult.message);
                const url = (inquiryInfo.inquiryType && inquiryInfo.inquiryPath) ?
                    `${inquiryInfo.inquiryPath}?selected=${inquiryInfo.inquiryType}` :
                    undefined
                router.replace(url || HOME_PATH)
            } else if (mutateResult.success && mutateResult.href) {
                router.replace(mutateResult.href)
            }
        } else {
            toast.success('ورود با موفقیت انجام شد.')
            const returnUrl = searchParam.get(RETURN_URL)
            const url = returnUrl || HOME_PATH
            router.replace(url)
        }
        setEnterCodeLoading(false)
    }

    async function authOTPAction() {
        if (!optLoading) {
            setOptLoading(true)
        }
        const actionResult = await authOTP(form.getValues('mobile'), application?.pattern_login || '')
        if (!actionResult.success) {
            toast.error(actionResult.message!)
            setOptLoading(false)
            return
        }
        setTempToken(actionResult.data!.token)
        setExpireTime(actionResult.data!.expire_time)
        setOptLoading(false)
        setFormStep(2)

    }

    async function handleGetCode() {
        setOptLoading(true)
        const mobileValid = await form.trigger('mobile')
        if (!mobileValid) {
            setOptLoading(false)
            return;
        }
        // const success = await handleRecaptcha();
        // if (success) {
        await authOTPAction()
        // }
        // setOptLoading(false)
    }

    async function onGetCodeButtonClick() {
        await handleGetCode()
    }


    function onBackClick() {
        form.reset()
        setExpireTime(undefined)
        setTempToken(undefined)
        setFormStep(1)
    }

    async function onMobileInputKeyUp(e: React.KeyboardEvent<HTMLInputElement>) {
        if (e.key === "Enter") {
            e.preventDefault();
            await handleGetCode()
        }
    }


    return (

        <Card className="relative w-full max-w-lg h-fit px-6 py-8">
            {!inquiryInfo && <CardHeader className="text-center">
                {/*<CardTitle className="text-2xl">خوش آمدید</CardTitle>*/}
            </CardHeader>}
            <CardContent>
                <Form {...form}>
                    {formStep === 2 && <div
                        className='absolute top-7 left-7 cursor-pointer'
                        onClick={onBackClick}
                    >
                        <ArrowRight className='text-primary' size={30}/>
                    </div>}
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8" autoComplete="off">
                        {inquiryInfo && <InquiryInfo data={inquiryInfo}/>}
                        <div
                            className=' py-2 flex flex-col items-center gap-4'>
                            {formStep === 1 &&
                                <div className='self-start mt-4'>
                                    {!inquiryInfo && <h3 className='text-lg text-gray-800'>ورود | ثبت نام</h3>}
                                    <p className='text-sm md:text-base text-gray-500 mt-4'>جهت ورود یا ثبت نام شماره
                                        موبایل خود
                                        را وارد
                                        کنید</p>
                                </div>
                            }
                            {
                                formStep === 2 && (
                                    <div className='self-start mt-4'>
                                        <h3 className='text-lg text-gray-800'>کد تایید را وارد کنید</h3>
                                        <p className='text-xs text-gray-600 mt-4'>{`کد تایید برای شماره ${form.getValues('mobile')} ارسال شد`}</p>
                                    </div>
                                )
                            }
                            {formStep === 1 && <FormField
                                control={form.control}
                                name="mobile"
                                render={({field}) => (
                                    <FormItem className='w-full'>
                                        <FormControl>
                                            <CustomInput
                                                autoFocus
                                                inputMode='numeric'
                                                align='center'
                                                type='text'
                                                onKeyDown={onMobileInputKeyUp}
                                                allowOnlyNumbers
                                                maxLength={11}
                                                placeholder='شماره موبایل ( *********09 )'
                                                value={field.value}
                                                onChange={(value) => field.onChange(value)}
                                            />
                                        </FormControl>
                                        <FormMessage className='text-sm'/>
                                    </FormItem>
                                )}
                            />}
                            {
                                formStep === 2 && (
                                    <FormField
                                        control={form.control}
                                        name="code"
                                        render={({field}) => (
                                            <FormItem className='w-full'>
                                                <FormControl>
                                                    <div className='flex w-full justify-center items-center'>
                                                        <CustomInput
                                                            autoFocus
                                                            type='text'
                                                            align='center'
                                                            inputMode='numeric'
                                                            allowOnlyNumbers
                                                            maxLength={5}
                                                            placeholder=''
                                                            value={field.value}
                                                            onChange={(value) => field.onChange(value)}
                                                        />
                                                    </div>
                                                </FormControl>
                                                <FormMessage className='text-center text-sm'/>
                                            </FormItem>
                                        )}
                                    />
                                )
                            }
                            <div className='w-full flex flex-col justify-center items-center gap-1 mt-2'>

                                {formStep === 2 && expireTime &&
                                    <div className='flex gap-1 items-center'>
                                        <CountDownTimer remainingTime={expireTime}
                                                        onComplete={() => setExpireTime(undefined)}/>
                                        <span className='text-sm text-gray-600'>مانده تا دریافت مجدد کد.</span>
                                    </div>
                                }
                                {formStep === 2 && expireTime === undefined && (
                                    <div>
                                <span
                                    className='text-sm text-gray-600'>
                                <span className='text-primary/80 cursor-pointer mx-1'
                                      onClick={authOTPAction}>دریافت مجدد</span>
                                    کد تایید</span>

                                    </div>
                                )}
                                {formStep === 1 ? <CustomButton
                                    variants='blue'
                                    loading={optLoading}
                                    disabled={optLoading}
                                    type="button"
                                    onClick={onGetCodeButtonClick}
                                >
                                    دریافت کد
                                </CustomButton> : (
                                    <CustomButton
                                        variants='blue'
                                        loading={enterCodeLoading || isLoading}
                                        disabled={enterCodeLoading || isLoading}
                                        className="mt-1"
                                        type="submit">
                                        ورود
                                    </CustomButton>
                                )}
                                <div className=''>
                          <span
                              className="text-sm text-gray-800">  در صورت ادامه شما<Link href="/rules"
                                                                                         className="text-blue-500 hover:underline"> شرایط استفاده از خدمات  </Link>           را می‌پذیرید</span>
                                </div>
                            </div>
                        </div>
                    </form>
                </Form>

            </CardContent>
        </Card>
    );
}
