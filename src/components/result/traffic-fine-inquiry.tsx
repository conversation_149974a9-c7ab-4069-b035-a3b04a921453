import {VehiclePaymentDetails} from "@/lib/types";
import {Card} from "@/components/ui/card";
import DisplayPlaque from "@/components/common/display-plaque";
import TrafficFineResultWithDetails from "@/components/result/traffic-fine-result-with-details";
import ChoiceWrapper from "@/components/common/choice-wrapper";
import TrafficFineResultNoDetails from "@/components/result/traffic-fine-result-no-details";
import CustomButton from "@/components/common/custom-button";
import {formatWithComma} from "@/utils/helpers";
import ReInquire from "@/components/result/re-inquiry";

type Props = {
    inquiryDetail: VehiclePaymentDetails
    isNew?: 'true' | 'false'
    newInquiryUrl: string
    inquiryPath?: string
}

export default function TrafficFineInquiry({inquiryDetail, isNew, newInquiryUrl, inquiryPath}: Props) {
    const isMotor = inquiryDetail.isMotor;
    const {middle, left, right, alphabet} = inquiryDetail.plaque
    const hasParameters = !inquiryDetail.description

    return (
        <Card className="relative w-full !h-fit max-w-xl px-5 md:px-10 !pt-0 !py-10">
            <div className="w-full justify-center items-center flex flex-col gap-y-7">
                <h2 className='text-[#212121] text-sm font-semibold'>نتیجه استعلام وسیله نقلیه شما</h2>
                <DisplayPlaque left={left || ""} right={right || ""} middle={middle || ""} alphabet={alphabet || ""}
                               isMotor={isMotor}/>
                {
                    (hasParameters && inquiryDetail.withDetails) && (
                        (inquiryDetail?.Details ?? []).map((item, index) => (
                            <TrafficFineResultWithDetails key={index} detail={item}/>
                        ))
                    )
                }
                {
                    (hasParameters && !inquiryDetail.withDetails) && (
                        <TrafficFineResultNoDetails inquiryDetails={inquiryDetail}/>
                    )
                }

                {<ChoiceWrapper
                    backgroundColor='#FFF5D8'
                    borderColor='#F7BC06'
                >
                    <div className='w-full flex flex-col gap-y-2 py-3 px-2'>
                        {
                            hasParameters && <>
                                {(isNew && isNew === 'false') && <div className='w-full'>
                                    <p className='text-sm text-justify text-destructive'>توجه: این استعلام در گذشته توسط شما
                                        انجام شده و
                                        ممکن است
                                        اطلاعات آن
                                        به ‌روز نباشد. برای مشاهده استعلام
                                        جدید، دکمه &quot;استعلام جدید&quot; را انتخاب کنید.</p>
                                </div>}
                                <div className='w-full text-xs flex mt-2 justify-between items-center'>
                                    <span>تاریخ استعلام:</span>
                                    <span className=''>{inquiryDetail.dateInquiry}</span>
                                </div>
                                <div className='w-full flex justify-between items-center'>
                                    <span>جمع کل:</span>
                                    <span
                                        className='text-[#000000]'>{formatWithComma(inquiryDetail.withDetails ? inquiryDetail!.TotalAmount!.toString() : inquiryDetail!.Amount!.toString())}<span
                                        className='text-xs mx-1'>تومان</span></span>
                                </div>
                            </>
                        }
                        {
                            !hasParameters &&
                            <p className='text-sm text-center'>{inquiryDetail.description}</p>
                        }
                    </div>
                </ChoiceWrapper>}
            </div>
            <div className="mt-3">
                {hasParameters && <CustomButton
                    target='_blank'
                    href={inquiryDetail.paymentUrl}
                    className='!py-4'>پرداخت
                    کلی</CustomButton>}
                <ReInquire
                    isNew={isNew}
                    inquiryPath={inquiryPath}
                    data={inquiryDetail}
                    newInquiryUrl={newInquiryUrl}
                />
            </div>
        </Card>
    );
}
