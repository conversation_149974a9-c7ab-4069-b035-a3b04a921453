'use client'

import {InquiryTypeEnum, ServiceComponentProps} from "@/lib/types";
import {useForm} from "react-hook-form";
import {sayadiNumberSchema, SayadiNumberType} from "@/lib/zod-schemas";
import {zodResolver} from "@hookform/resolvers/zod";
import {Form, FormControl, FormField, FormItem, FormMessage} from "@/components/ui/form";
import CustomInput from "@/components/common/custom-input";
import CustomButton from "@/components/common/custom-button";
import InquiryPrice from "@/components/common/inquiry/inquiry-price";

export default function SayadiCheckForm({isLoading, onSubmit, price}: ServiceComponentProps) {
    const formDepositToIban = useForm<SayadiNumberType>({
        resolver: zodResolver(sayadiNumberSchema),
        mode: 'onSubmit',
        defaultValues: {
            sayadId: ""
        },
    })

    async function handleSubmit(values: any) {
        if (isLoading) return;
        onSubmit?.(values, InquiryTypeEnum.Ghabzino_SayadCheckInquiry)
    }

    return (
        <>
            <Form {...formDepositToIban}>
                <form
                    onSubmit={formDepositToIban.handleSubmit((values) => handleSubmit(values))}
                    className="space-y-6">
                    <FormField
                        control={formDepositToIban.control}
                        name="sayadId"
                        render={({field}) => (
                            <FormItem className='w-full'>
                                <p className='font-light mb-3 text-neutral-700 text-sm md:text-base'>شناسه صیادی
                                    (شماره 16 رقمی) چک را
                                    وارد کنید، سپس دکمه
                                    استعلام را
                                    انتخاب کنید
                                </p>
                                <FormControl>
                                    <CustomInput
                                        autoFocus
                                        type='text'
                                        allowOnlyNumbers
                                        inputMode='numeric'
                                        maxLength={16}
                                        placeholder='شناسه صیادی را وارد کنید'
                                        {...field}
                                        value={field.value}
                                        onChange={(value) => field.onChange(value)}
                                    />
                                </FormControl>
                                <FormMessage className='text-sm'/>
                            </FormItem>
                        )}
                    />
                    <CustomButton
                        loading={isLoading}
                        disabled={isLoading}
                        variants='blue'
                        className="mt-1"
                        type="submit">استعلام
                    </CustomButton>
                </form>
            </Form>
            <InquiryPrice price={price}/>
        </>
    );
}
