import {EnvConfig} from "@/lib/types";

const envConfig = (): EnvConfig => {
    return {
        CONVERSION: {
            API_SECRET: process.env.CONVERSION_API_SECRET || "",
            BASEURL: process.env.CONVERSION_BASEURL || ""
        },
        MONGODB_URI: process.env.MONGODB_URI || "",
        BASE_URL: process.env.BASE_URL || "",
        NODE_ENV: process.env.NODE_ENV || "development",
        INQUIRY_PRICE: process.env.INQUIRY_PRICE || "",
        GHABZINO: {
            BASEURL: process.env.GHABZINO_BASEURL || "",
            TOKEN: process.env.G<PERSON>BZINO_TOKEN || "",
            PAY_BILLS_THROUGH_PORTAL_TERMINALID: process.env.GHABZINO_PAY_BILLS_THROUGH_PORTAL_TERMINALID || "",
            MOTOR_TRAFFIC_FINES_INQUIRY_BY_PLATE_NUMBER_URL: process.env.GHABZINO_MOTOR_TRAFFIC_FINES_INQUIRY_BY_PLATE_NUMBER_URL || "",
            MOTOR_TRAFFIC_FINES_INQUIRY_BY_PLATE_NUMBER_NO_DETAIL_URL: process.env.GHABZINO_MOTOR_TRAFFIC_FINES_INQUIRY_BY_PLATE_NUMBER_NO_DETAIL_URL || "",
            TRAFFIC_FINES_INQUIRY_BY_PLATE_NUMBER_URL: process.env.GHABZINO_TRAFFIC_FINES_INQUIRY_BY_PLATE_NUMBER_URL || "",
            TRAFFIC_FINES_INQUIRY_BY_PLATE_NUMBER_NO_DETAIL_URL: process.env.GHABZINO_TRAFFIC_FINES_INQUIRY_BY_PLATE_NUMBER_NO_DETAIL_URL || "",
            REPORT_NEW_BILL_PAYMENT_URL: process.env.GHABZINO_REPORT_NEW_BILL_PAYMENT_URL || "",
            SAYAD_CHECK_INQUIRY_URL: process.env.GHABZINO_SAYAD_CHECK_URL || "",
            SHEBA_INQUIRY_ACCOUNT_TO_SHEBA: process.env.GHABZINO_SHEBA_INQUIRY_ACCOUNT_TO_SHEBA || "",
            SHEBA_INQUIRY_CARD_TO_SHEBA: process.env.GHABZINO_SHEBA_INQUIRY_CARD_TO_SHEBA || "",
            REPORT_NEW_BILL_REDIRECT_LINK: process.env.GHABZINO_REPORT_NEW_BILL_REDIRECT_LINK || "",
            REPORT_NEW_BILL_REDIRECT_LINK_TITLE: process.env.GHABZINO_REPORT_NEW_BILL_REDIRECT_LINK_TITLE || "",
        },
        ZARINPAL: {
            MERCHANT_ID_PRODUCTION: process.env.ZARINPAL_MERCHANT_ID_PRODUCTION || "",
            MERCHANT_ID_DEVELOPMENT: process.env.ZARINPAL_MERCHANT_ID_DEVELOPMENT || "",
            TERMINAL_ID: process.env.ZARINPAL_TERMINAL_ID || ""
        },
        JWT: {
            SECRET: process.env.JWT_SECRET || "",
            EXPIRES_IN: process.env.JWT_EXPIRES_IN || ""
        },
        OTP_EXPIRES_IN: process.env.OTP_EXPIRES_IN || '',
        JIBIT: {
            API_KEY: process.env.JIBIT_API_KEY || "",
            SECRET_KEY: process.env.JIBIT_SECRET_KEY || "",
            BASE_URL: process.env.JIBIT_BASE_URL || ""
        },
        MELI_PAYAMAK: {
            USERNAME: process.env.MELI_PAYAMAK_USERNAME || "",
            PASSWORD: process.env.MELI_PAYAMAK_PASSWORD || "",
            BODY_ID: process.env.MELLI_PAYAMAK_BODY_ID || ""
        }
    }
}


export default envConfig