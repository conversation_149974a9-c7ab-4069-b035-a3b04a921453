import Link from "next/link";
import {DEPO_PATH, HOME_PATH} from "@/lib/routes";
import {Button} from "@/components/ui/button";
import {ArrowRight} from "lucide-react";
import {InquiryStatusPaymentResult, PaymentStatusResult} from "@/lib/types";
import CustomButton from "@/components/common/custom-button";

type Props = {
    paymentStatus: PaymentStatusResult,
    inquiryStatus: InquiryStatusPaymentResult,
    inquiryResultUrl?: string,
    newInquiryUrl?: string,
}

export default function PaymentButtonActions({
                                                 paymentStatus,
                                                 inquiryStatus,
                                                 inquiryResultUrl,
                                                 newInquiryUrl
                                             }: Props) {

    return (
        <div className="grid gap-3">
            {
                paymentStatus === "failed" ? (
                    <Link href={DEPO_PATH}>
                        <Button className="w-full bg-red-600 hover:bg-red-700 cursor-pointer">
                            تلاش مجدد برای پرداخت
                        </Button>
                    </Link>
                ) : inquiryStatus === "failed" ? (
                        <CustomButton
                            href={newInquiryUrl || HOME_PATH}
                            className="w-full !bg-amber-600 !hover:bg-amber-700 cursor-pointer !px-4 !py-2 !h-9">
                            تلاش مجدد برای استعلام
                        </CustomButton>
                    ) :
                    inquiryStatus === "success" ? (
                        <Link href={inquiryResultUrl ?? '#'}>
                            <Button className="w-full bg-green-600 hover:bg-green-700 cursor-pointer">
                                مشاهده استعلام
                            </Button>
                        </Link>
                    ) : null
            }

            <Link href={HOME_PATH}>
                <Button variant="outline" className="w-full border-slate-200 cursor-pointer">
                    بازگشت به صفحه اصلی
                    <ArrowRight className="mr-2 h-4 w-4"/>
                </Button>
            </Link>
        </div>
    );
}
