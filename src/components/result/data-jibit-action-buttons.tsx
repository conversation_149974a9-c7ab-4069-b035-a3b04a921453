'use client'

import CustomButton from "@/components/common/custom-button";
import {Check, Copy, Download} from "lucide-react";
import {BankInfoType, JibitResultType} from "@/lib/types";
import {useState} from "react";
import {translateBankInfoStatus} from "@/utils/helpers";

type Props = {
    jibitResult: JibitResultType,
    bankInfo: BankInfoType | undefined,
}

export default function DataJibitActionButtons({bankInfo, jibitResult}: Props) {
    const [copied, setCopied] = useState(false)


    function generateText(result: JibitResultType) {
        let text = ''
        text += result?.bank ? `نام بانک: ${bankInfo?.bankNameFarsi}\n` : '';
        if (result?.owners && result.owners.length > 0) {
            result.owners.map((item, index) => {
                text += `نام صاحب حساب: ${item}`;
                if (index !== result.owners.length - 1) {
                    text += ', '
                } else {
                    text += '\n'
                }
            })
        }
        text += result?.ownerName ? `نام صاحب حساب: ${result?.ownerName}\n` : ''
        text += result?.status ? `وضعیت حساب: ${translateBankInfoStatus(result.status)}\n` : '';
        text += result?.depositNumber ? `شماره حساب: ${result.depositNumber}\n` : '';
        text += result?.iban ? `شماره شبا: ${result.iban}\n` : '';

        return text;
    }

    const copyAll = async (result: JibitResultType) => {

        const text = generateText(result);
        try {
            await navigator.clipboard.writeText(text);
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        } catch (err) {
            console.error("Failed to copy text: ", err);
        }
    }

    function downloadTextFile(result: JibitResultType, filename = 'info.txt') {
        const text = generateText(result);
        const blob = new Blob([text], {type: 'text/plain'});
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    return (
        <div className="grid grid-cols-2 gap-4 mt-8">
            <CustomButton
                variants='blue'
                onClick={() => copyAll(jibitResult)}
            >
                {copied ? <Check className="h-5 w-5"/> : <Copy className="h-5 w-5"/>}
                <span className='mx-2'>{copied ? "کپی شد" : "کپی کردن همه"}</span>
            </CustomButton>
            <CustomButton
                variants='blue'
                onClick={() => downloadTextFile(jibitResult)}
            >
                <Download className="h-5 w-5"/>
                <span className='mx-2'>دانلود</span>
            </CustomButton>
        </div>
    );
}
