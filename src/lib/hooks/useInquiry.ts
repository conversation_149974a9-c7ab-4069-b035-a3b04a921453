'use client'

import {useState} from "react";
import {useAuth} from "@/lib/hooks/useAuth";
import usePathUrlHelper from "./usePathUrlHelper";
import {InquiryQueryParams} from "@/lib/types";
import {inquireByServiceType} from "@/features/inquiry/inquiry.action";
import status from "http-status";

type InquiryPostResult = {
    status?: number,
    href?: string,
    message?: string,
    success: boolean
}

export default function useInquiry() {

    const {toLoginUrl, toPaymentUrl, toInquiryResultUrl} = usePathUrlHelper();
    const {reFetchUser} = useAuth()
    const [isLoading, setIsLoading] = useState(false)

    const mutate = async (params: InquiryQueryParams): Promise<InquiryPostResult> => {
        setIsLoading(true)

        const actionResult = await inquireByServiceType(params)

        if (!actionResult.success) {

            if (actionResult.status === status.UNAUTHORIZED) {

                return {
                    href: toLoginUrl<InquiryQueryParams>({
                        ...params,
                    }, {currentUrlQuery: false}),
                    message: actionResult.message,
                    success: false,
                    status: actionResult.status
                }
            }

            if (actionResult.status === status.PAYMENT_REQUIRED) {
                return {
                    href: toPaymentUrl<InquiryQueryParams>({
                        ...params,
                    }, {currentUrlQuery: false}),
                    message: actionResult.message,
                    success: false,
                    status: actionResult.status
                }
            }
            setIsLoading(false)
            return {
                message: actionResult.message,
                success: false,
                status: actionResult.status || 500
            };
        }

        await reFetchUser();
        return {
            success: true,
            href: toInquiryResultUrl({
                trackingNumber: actionResult.data!.trackingNumber!,
                isNew: actionResult.data!.isNew,
                inquiryPath: params.inquiryPath,
                inquiryType: params.inquiryType
            }),

        }

    }

    return {mutate, isLoading, setIsLoading}
}
