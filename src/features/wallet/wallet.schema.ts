import {Schema, model, Model, models} from 'mongoose';
import {IWallet, TransactionType} from "@/lib/types";

const WalletSchema = new Schema<IWallet>(
    {
        user: {
            type: Schema.Types.ObjectId,
            ref: 'User',
            required: true,
        },
        phone: {
            type: String,
            required: true,
        },
        amount: {
            type: Number,
            required: true,
        },
        balance_before: {
            type: Number,
            required: true,
            min: 0
        },
        balance_after: {
            type: Number,
            required: true,
            min: 0
        },
        refundId: {
            type: Schema.Types.ObjectId,
            ref: 'Wallet',
        },
        source: {
            type: String,
            required: true,
        },
        reference_type: {
            type: String,
            enum: Object.values(TransactionType),
        },
        reference_id: {
            type: Schema.Types.ObjectId,
        },
        reason: {
            type: String,
        }
    },
    {
        collection: 'wallet',
        timestamps: true,
        optimisticConcurrency: true,
    }
);

WalletSchema.index({reference_type: 1, reference_id: 1});

const WalletModel: Model<IWallet> =
    (models.Wallet) ||
    model<IWallet>('Wallet', WalletSchema);

export default WalletModel;