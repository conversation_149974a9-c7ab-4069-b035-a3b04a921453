import Fetch<PERSON><PERSON> from "@/lib/fetch-api";
import envConfig from "@/lib/config.env";
import JibitTokenManager from "@/features/jibit/jibit.tokenManager";
import {BankInquiryType} from "@/lib/types";

export default class JibitFetchClient {
    private fetchInstance: FetchApi;

    private constructor(accessToken: string) {
        const env = envConfig();
        const baseUrl = env.JIBIT.BASE_URL;
        this.fetchInstance = new FetchApi(baseUrl, accessToken);
    }

    static async create() {
        const accessToken = await JibitTokenManager.getToken();
        return new JibitFetchClient(accessToken);
    }

    async convertCardToIban(cardNumber: string) {
        return this.fetchInstance.get<BankInquiryType>(`/v1/cards?number=${cardNumber}&iban=true`);
    }

    async convertCardToDeposit(cardNumber: string) {
        return this.fetchInstance.get<BankInquiryType>(`/v1/cards?number=${cardNumber}&deposit=true`);
    }

    async convertDepositToIban(bank: string, depositNumber: string) {
        return this.fetchInstance.get<BankInquiryType>(`/v1/deposits?bank=${bank}&number=${depositNumber}&iban=true`);
    }

    async iban(iban: string) {
        return this.fetchInstance.get<BankInquiryType>(`/v1/ibans?value=${iban}`);
    }

    async cardNumber(cardNumber: string) {
        return this.fetchInstance.get<BankInquiryType>(`/v1/cards?number=${cardNumber}`);
    }
}