import {Schema, Model, models, model} from 'mongoose';
import {IInquiryR<PERSON>ult, InquiryStatus, InquiryTypeEnum, ProviderType} from "@/lib/types";


const InquirySchema: Schema = new Schema<IInquiryResult>(
    {
        trackingNumber: {
            type: String,
            unique: true,
            index: true,
        },
        trackingNumberExpireTime: {
            type: Date,
        },
        paid: {
            type: Boolean,
            required: true,
        },
        bank: {
            type: String,
        },
        accountTypeName: {
            type: String,
        },
        retryCount: {
            type: Number,
            default: 0,
        },
        depositNumber: {
            type: String,
        },
        balance: {
            type: Number,
        },
        balance_after: {
            type: Number,
        },
        provider: {
            type: String,
            enum: Object.values(ProviderType),
            required: true,
        },
        sayadId: {
            type: String,
        },
        withDetails: {
            type: Boolean,
        },
        inquiry_nationalId: {
            type: String,
        },
        plaque: {
            type: String,
        },
        inquiry_mobileNumber: {
            type: String,
        },
        iban: {
            type: String,
        },
        cardNumber: {
            type: String,
        },
        inquiryStatus: {
            type: String,
            enum: Object.values(InquiryStatus),
            required: true,
        },
        source: {
            type: String,
            required: true,
        },
        ip: {
            type: String,
            required: true
        },
        agent: {
            type: String,
            required: true
        },
        user: {
            type: Schema.Types.ObjectId,
            ref: 'User',
            required: true
        },
        phone: {
            type: String,
            required: true,
        },
        inquiryType: {
            type: String,
            enum: Object.values(InquiryTypeEnum),
            required: true
        },
        inquiryParams: Schema.Types.Mixed,
        inquiryResult: Schema.Types.Mixed,
    },
    {
        timestamps: true,
        collection: 'inquiry_app_results',
    }
);

const InquiryModel: Model<IInquiryResult> =
    (models.Inquiry) ||
    model<IInquiryResult>("Inquiry", InquirySchema);

export default InquiryModel;

