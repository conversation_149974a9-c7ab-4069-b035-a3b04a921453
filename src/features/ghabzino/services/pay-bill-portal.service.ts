import {
    GenericResponse,
    GhabzinoReportNewBillPayment,
    IInquiryService,
    PayBillsRequest
} from "@/lib/types";
import envConfig from "@/lib/config.env";
import tokenService from "@/lib/services/token.service";
import {handleInquiryServiceError} from "@/utils/helpers-server";
import GhabzinoFetchClient from "@/features/ghabzino/ghabzino.fetchClient";

type PayBillPortalInput = {
    billId: string,
    paymentId: string,
    phone: string
}

class PayBillPortalService implements IInquiryService<PayBillPortalInput, GhabzinoReportNewBillPayment | undefined> {


    async lookup({
                     billId,
                     paymentId,
                     phone,
                 }: PayBillPortalInput): Promise<GenericResponse<GhabzinoReportNewBillPayment | undefined>> {
        try {
            const env = envConfig()

            const redirectLink = env.GHABZINO.REPORT_NEW_BILL_REDIRECT_LINK;
            const redirectLinkTitle = env.GHABZINO.REPORT_NEW_BILL_REDIRECT_LINK;
            const terminalId = env.GHABZINO.PAY_BILLS_THROUGH_PORTAL_TERMINALID

            const fetchParameters: PayBillsRequest = {
                TerminalId: terminalId,
                RedirectLinkTitle: redirectLinkTitle,
                MobileNumber: phone,
                Bills: [
                    {
                        BillID: billId,
                        PaymentID: paymentId
                    }
                ],
                TraceNumber: tokenService.generateUUID(),
                RedirectLink: redirectLink
            }

            const response = await GhabzinoFetchClient.reportNewBillPayment(fetchParameters)

            const successCodes = ['G00000'];
            const success = successCodes.includes(response?.Status?.Code);

            if (success) {
                return {
                    success: 'SUCCESS',
                    data: response.Parameters
                };
            }

            return {
                success: 'FAILED',
                message: response?.Status?.Description || 'خطای ناشناخته‌ای رخ داده است',
            };

        } catch (error: any) {
            return handleInquiryServiceError(error);
        }
    }

}

export default PayBillPortalService;