import Link from "next/link";
import {JSX} from "react";
import {cn} from "@/lib/utils";

type ServiceCardProps = {
    title: string;
    path: string;
    icon: JSX.Element;
    bgClass: string;
};

export default function ServiceLink({title, path, icon, bgClass}: ServiceCardProps) {
    return (
        <Link href={path} className="flex flex-col items-center cursor-pointer">
            <div
                className={cn('rounded-full p-5 w-16 md:w-20 h-16 md:h-20 flex items-center justify-center mb-4', bgClass)}>
                {icon}
            </div>
            <span className="text-center font-semibold text-neutral-700 text-sm md:text-base">
            {title}
        </span>
        </Link>
    );
}
