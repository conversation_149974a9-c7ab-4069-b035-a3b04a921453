'use client'

import {useEffect} from 'react'

export default function Error({
                                  error,
                                  reset,
                              }: {
    error: Error & { digest?: string }
    reset: () => void
}) {
    useEffect(() => {
    }, [error])

    return (
        <div>
            <h2>با عرض پوزش مشکلی در سایت پیش آمد</h2>
            <button
                onClick={
                    // Attempt to recover by trying to re-render the segment
                    () => reset()
                }
            >
                تلاش مجدد
            </button>
        </div>
    )
}