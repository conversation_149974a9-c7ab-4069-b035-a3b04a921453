import {ClientSession, Document, Types} from "mongoose";
import {SOURCE_NAME} from "@/lib/constants";
import {ReactNode} from "react";
import {JWTPayload} from "jose";

export interface EnvConfig {
    MONGODB_URI: string;
    CONVERSION: {
        API_SECRET: string;
        BASEURL: string;
    }
    BASE_URL: string;
    NODE_ENV: "development" | "production" | "test";
    INQUIRY_PRICE: string;
    ZARINPAL: {
        MERCHANT_ID_PRODUCTION: string;
        MERCHANT_ID_DEVELOPMENT: string;
        TERMINAL_ID: string;
    };
    GHABZINO: {
        BASEURL: string;
        TOKEN: string;
        PAY_BILLS_THROUGH_PORTAL_TERMINALID: string;
        MOTOR_TRAFFIC_FINES_INQUIRY_BY_PLATE_NUMBER_URL: string;
        MOTOR_TRAFFIC_FINES_INQUIRY_BY_PLATE_NUMBER_NO_DETAIL_URL: string;
        TRAFFIC_FINES_INQUIRY_BY_PLATE_NUMBER_URL: string;
        TRAFFIC_FINES_INQUIRY_BY_PLATE_NUMBER_NO_DETAIL_URL: string;
        REPORT_NEW_BILL_PAYMENT_URL: string;
        SAYAD_CHECK_INQUIRY_URL: string;
        REPORT_NEW_BILL_REDIRECT_LINK: string;
        REPORT_NEW_BILL_REDIRECT_LINK_TITLE: string;
        SHEBA_INQUIRY_ACCOUNT_TO_SHEBA: string
        SHEBA_INQUIRY_CARD_TO_SHEBA: string
    },
    JWT: {
        SECRET: string;
        EXPIRES_IN: string;
    };
    OTP_EXPIRES_IN: string;
    JIBIT: {
        API_KEY: string;
        SECRET_KEY: string;
        BASE_URL: string;
    }
    MELI_PAYAMAK: {
        USERNAME: string;
        PASSWORD: string;
        BODY_ID: string;
    }
}

export enum AccountTypeEnum {
    Deposit = 'Deposit',
    Loan = 'Loan'
}

export interface IInquiryService<Input, Output> {
    lookup(params: Input, session?: ClientSession | null): Promise<GenericResponse<Output>>;
}

export type GhabzinoReportNewBillPayment = {
    PaymentKey: number;
    PaymentLink: string;
    Bills: ReportNewPaymentBill[]
}

export type ReportNewPaymentBill = {
    BillID: string;
    PaymentID: string;
    Amount: number;
    RecordNumber: string;
    BillType: string;
    BillTypeShowName: string;
    Description: string;
    ValidForPayment: boolean;
}


export type InquiryQueryParams = InquiryType & {
    message?: string;
}

export interface UserPayload {
    phone?: string
    balance: number
    admin?: boolean
}

export type SMSResponseType = {
    Value: string;
    RetStatus: number;
    StrRetStatus: string;
};

export type GenericResponse<T1 = undefined, T2 = undefined> = {
    success: 'SUCCESS' | 'PENDING' | 'FAILED',
    data?: T1
    errors?: T2
    alreadyPaid?: boolean
    message?: string
}


export type PaymentResultResponse = {
    payment_url: string
}

export type ZarinpalPaymentRequestResponse = {
    url: string,
    authority?: string,
    fee_type: string,
    fee: string,
    code: number,
    message: string,
}

export type InquireResponse = {
    trackingNumber: string
    isNew: boolean
}

export type JibitResultType = {
    cardNumber?: string,
    iban?: string,
    status?: BankInfoStatus,
    bank?: string,
    depositNumber?: string,
    inquiryType: InquiryTypeEnum,
    owners: string[],
    ownerName?: string,
}

export type BankInquiryType = {
    number: string,
    iban?: string,
    type: string,
    ibanInfo?: IbanInfo
    depositInfo?: DepositInfo
    depositToIBANInfo?: DepositToIBANInfo
    cardInfo?: CardInfo
}

export type CardInfo = {
    bank: string
    type: string
    ownerName: string
    depositNumber: string
}

export type JibitFailedResultType = {
    code: string,
    message: string,
}

export type DepositToIBANInfo = {
    bank: string
    iban: string
}

export type DepositInfo = {
    bank: string,
    depositNumber: string,
}

export type IbanInfo = {
    bank: string,
    depositNumber: string,
    iban: string,
    status: BankInfoStatus
    owners: Owner[]
}

export enum BankInfoStatus {
    ACTIVE = "ACTIVE",
    BLOCK_WITH_DEPOSIT = "BLOCK_WITH_DEPOSIT",
    BLOCK_WITHOUT_DEPOSIT = "BLOCK_WITHOUT_DEPOSIT",
    IDLE = "IDLE",
    UNKNOWN = "UNKNOWN",
}

export type Owner = {
    firstName: string,
    lastName: string,
}

export class ApiService {
    serverCookies?: string
    headers?: Record<string, string> = {}
    payload?: any
}

export interface ResponseResult<T> {
    data: T
    success: boolean
    status: number
}

type Bill = {
    BillID: string;
    PaymentID: string;
};

export type PayBillsRequest = {
    TerminalId: string;
    RedirectLinkTitle: string;
    MobileNumber: string;
    Bills: Bill[];
    TraceNumber: string;
    RedirectLink: string;
};

export type TrafficFinesFetchParams = {
    WalletIdentifier: string,
    TraceNumber: string,
    Left: string,
    Right: string,
    MobileNumber?: string | null,
    NationalID?: string | null
    Mid?: string
    Alphabet?: string,
}

export type CategoryServiceUpsertInput = {
    id?: string,
    title: string,
    slug: string,
    active?: boolean,
    imagePath?: string
}

export interface Category {
    title: string
    slug: CategorySlugEnum
    active: boolean
    path: string
    description: string
}

export interface JWTUserPayload extends JWTPayload {
    phone?: string,
    admin?: boolean
}

export type AuthResponse = {
    message: string
    token: string
    expire_time: number
}

export type PaymentRequestInput = {
    amount: number,
    description?: string,
    callbackUrl: string,
    inquiry?: InquiryType
}

export type ServiceType = {
    key: InquiryTypeEnum
    provider: ProviderType,
    value: SettingsValueTypeEnum
    title: string
    price: string
    validPeriod: number
    inquiryPolicy: InquiryPolicyEnum
    category: CategorySlugEnum
    version?: number
    env: 'development' | 'production'
}

export enum InquiryPolicyEnum {
    'Show_Last_valid' = 'نمایش آخرین استعلام معتبر',
    'Create_new' = 'ایحاد استعلام جدید',
}

export type VerificationArgs = {
    phone: string,
    code: string,
    token: string,
}

export type VerificationResponse = {
    message: string
    access_token: string
    token_type: string
    balance: number
    expire_at: number
}

export type ServiceResult = {
    id: string,
    trackingNumber: string
}

export type GetUserResponse = {
    phone: string,
    balance: number
    admin: boolean
}

export type BankInfoType = {
    bankId: string;
    bankName: string;
    bankNameFarsi: string;
    icon?: ReactNode;
    iconSmall?: ReactNode;
    ghabzino: boolean;
}


export interface ActionResult<T = undefined> {
    message?: string
    errors?: string[] | string
    success: boolean
    status?: number
    data?: T
}

export enum ViolationTypeEnum {
    WITH_INFO = "withInfo",
    WITHOUT_INFO = "withoutInfo",
}

export enum InquiryTypeEnum {
    Jibit_DepositToIban = 'deposit-to-iban-a',
    Jibit_CardToDeposit = 'card-to-deposit-a',
    Jibit_CardToIban = 'card-to-iban-a',
    Jibit_IbanInquiry = 'iban-inquiry-a',
    Jibit_CardInquiry = 'card-inquiry-a',
    Ghabzino_SayadCheckInquiry = 'sayad-inquiry-b',
    Ghanzino_KhalafiKhodro = 'khalafi-khodro-b',
    Ghanzino_KhalafiMotor = 'khalafi-motor-b',
    Ghanzino_DepositToIban = 'deposit-to-iban-b',
    Ghanzino_CardToIban = 'card-to-iban-b',
}

export type ZarinpalVarificationResponse = {
    code: number,
    ref_id: number,
    card_pan: string,
    card_hash: string,
    fee_type: string,
    fee: string,
    message: string,
}

// services types

export type CreateTokenInputType = {
    phone: string;
    ip: string;
    agent: string;
    source: string;
    accessToken: string;
    otpToken: string;
    expiredIn: Date;
};

export type WalletCreateInputType = {
    userId: string,
    phone: string;
    amount: number;
    balanceAfter: number;
    referenceType?: TransactionType;
    referenceId?: string;
    source: string
    reason?: string;
    refundId?: string;
}

// mongoose types

interface IBase extends Document {
    _id: Types.ObjectId
    createdAt?: Date;
    updatedAt?: Date;
    __v: number;
}

export type ServiceComponentProps = {
    isLoading: boolean;
    onSubmit: (value: any, type: InquiryTypeEnum) => void
    price: string
}

export enum Role {
    User = 'user',
    Admin = 'admin',
}

export interface IUser extends IBase {
    phone: string;
    balance: number;
    admin?: boolean;
    source: string;
    lock: number;
}

export enum ServiceCategory {
    BankService
}

export enum SettingsValueTypeEnum {
    ACTIVE = "ACTIVE",
    DEACTIVE = "DEACTIVE"
}

export interface IService extends IBase {
    key: InquiryTypeEnum;
    provider: ProviderType;
    title: string;
    value: SettingsValueTypeEnum;
    price: number;
    category: CategorySlugEnum;
    source: typeof SOURCE_NAME
    validPeriod: number
    inquiryPolicy: InquiryPolicyEnum
    env: 'development' | 'production'
}

export interface ICategory extends IBase {
    title: string;
    path: string;
    slug: CategorySlugEnum;
    active: boolean;
    source: typeof SOURCE_NAME;
    description: string;
}

export interface ApplicationSeed {
    pattern_login: string;
    status: boolean;
    title: string;
    merchent_id?: string;
}

export interface ApplicationType extends ApplicationSeed {
    applicationId: string
    source?: string;
    subset?: typeof SOURCE_NAME;
    ga_apiSecret?: string;
    ga_measurementId?: string;
    ga_containerId?: string;
    merchent_id?: string;
}

export interface IApplication extends ApplicationType, IBase {
}

export enum CategorySlugEnum {
    BANKING_CONVERSION_INQUIRY = 'banking-conversion-services',
    SAYADI_CHECK_INQUIRY = 'sayadi-inquiry-services',
    KHALAFI_INQUIRY = 'khalafi-inquiry-services',
    BANKING_INQUIRY = 'banking-inquiry-services',

}

export interface IWalletConflict extends IBase {
    phone?: string;
    user: IUser | Types.ObjectId
    balance_in_wallet?: number;
    balance_in_user?: number;
    description: string;
    source?: string;
}

export interface IUserToken extends IBase {
    access_token: string;
    token: string;
    phone: string;
    status: 0 | 1;
    source: string;
    ip: string;
    agent: string;
    expiredIn: Date;
}


export type GhabzinoStatusType = {
    Code: string,
    Description: string
}

export type GhabzinoResponse<T> = {
    Parameters: T,
    Status: GhabzinoStatusType
}

export type GhabzinoSayadiInquiryResult = {
    Color: string;
    Date: string;
    Description: string;
    OwnerName: string;
    SayadID: string;
};

export type GhabzinoResultType = {
    inquiryType: InquiryTypeEnum;
    sayadiResult?: GhabzinoSayadiInquiryResult;
    description?: string;
    sayadId?: string;
}

export enum ProviderType {
    JIBIT = 'jibit',
    GHABZINO = 'ghabzino',
}

export type GhabzinoTrafficDetails = {
    Amount: number;
    BillID: string;
    City: string | null;
    DateTime: string;
    Delivery: string;
    HasImage: boolean;
    Location: string;
    OfficerIdentificationCode: string;
    PaymentID: string;
    SerialNumber: string;
    Type: string;
    TypeCode: string;
    UniqueID: string;
    PaymentUrl: string
}

export type GhabzinoTrafficInquiryResult = {
    BillID: string;
    Details?: Array<GhabzinoTrafficDetails>;
    PaymentID?: string;
    PlateNumber?: string;
    TotalAmount?: number;
    ComplaintStatus?: string;
    ComplaintCode?: string;
    Amount?: number;
}

export type VehiclePaymentDetails = GhabzinoTrafficInquiryResult & {
    isMotor: boolean;
    withDetails: boolean
    plaque: PlaqueType
    paymentUrl: string
    dateInquiry: string
    inquiryType: InquiryTypeEnum
    phoneNumber?: string
    nationalId?: string;
    inquiryUrl?: string
    description?: string
}

export type PlaqueType = {
    left: string,
    alphabet?: string,
    right: string,
    middle?: string
}

export interface BaseInquiry {
    provider: ProviderType;
    inquiryType: InquiryTypeEnum;
    phone: string;
    user: IUser | Types.ObjectId | string;
    source: string;
    depositNumber?: string;
    accountTypeName?: string;
    bank?: string;
    sayadId?: string;
    iban?: string;
    cardNumber?: string;
    inquiryStatus: InquiryStatus
    plaque?: string,
    inquiry_nationalId?: string | null,
    inquiry_mobileNumber?: string | null,
    paid: boolean;
    validPeriod?: number
    withDetails?: boolean;
    balance?: number,
    balance_after?: number,
}

export interface InquiryServiceCreateInput<T1, T2> extends BaseInquiry {
    inquiryResult?: T1,
    inquiryParams?: T2
}

export interface IInquiryResult extends IBase, BaseInquiry {
    ip: string;
    agent: string;
    trackingNumber: string;
    trackingNumberExpireTime?: Date;
    source: string;
    inquiryParams?: GhabzinoTrafficInquiryParams;
    inquiryResult?: any;
    inquiryUrl?: string
    retryCount: number;
    balance?: number,
    balance_after?: number
}

export enum PaymentGate {
    ZARINPAL = 'zarinpal',
}

export type BaseParamsType = {
    userId: string,
    phone: string,
    source: string
    validPeriod?: number,
    balance?: number,
    balance_after?: number,

}

export enum InquiryStatus {
    SUCCESS = 'success',
    FAILED = 'failed',
    PENDING = 'pending',

}

export type GhabzinoTrafficInquiryServiceParams = {
    Left: string;
    Mid?: string;
    Right: string;
    Alphabet?: string;
    MobileNumber?: string | null;
    NationalID?: string | null;
    isMotor: boolean;
    withDetails: boolean;
    inquiryType: InquiryTypeEnum;
}

export type GhabzinoTrafficInquiryParams = GhabzinoTrafficInquiryServiceParams & {
    withDetails: boolean;
    isMotor: boolean;
    WalletIdentifier: string;
    TraceNumber: string;
};

export enum PaymentStatus {
    PENDING = 'pending',
    SUCCESS = 'success',
    FAILED = 'failed',
}

export type ViolationType = {
    withDetails?: 'true' | 'false';
    phoneNumber?: string
    nationalCode?: string,
    middle?: string,
    left?: string,
    right?: string,
    alphabet?: string,
    isMotor?: 'true' | 'false',
}

export type GhabzinoBankType = {
    bankName?: string,
    bankShowName?: string,
    colorCode?: string,
    extraInfo: string | null,
    imageUrl?: string,
    shebaNumber?: string,
    whiteImageUrl?: string
    inquiryType: InquiryTypeEnum
    description?: string
    depositNumber?: string
    cardNumber?: string
}

export type GhabzinoBankResult = {
    BankName: string,
    BankShowName: string,
    ColorCode: string,
    ExtraInfo: string | null,
    ImageUrl: string,
    ShebaNumber: string,
    WhiteImageUrl: string
}

export type BankType = {
    depositNumber?: string;
    cardNumber?: string;
    bank?: string;
    sayadId?: string;
    iban?: string;
    accountTypeName?: string;
}

export type InquiryType = ViolationType & BankType & {
    inquiryType: InquiryTypeEnum;
    inquiryPath?: string;
    newInquiry?: 'true' | 'false';
}

export type InquiryStatusPaymentResult = 'pending' | 'no-query' | 'success' | 'failed'

export type PaymentStatusResult = 'success' | 'failed'


export type VerifyPaymentResponse = {
    hasInquiry: boolean,
    inquiry?: InquiryType,
    ref_id: number,
    amount: number,
}

export interface ITransaction extends IBase {
    gate: PaymentGate;
    terminalId: string;
    phone: string;
    amount: number;
    paymentCode: string;
    source: string
    authority: string,
    cardNumber?: string | null;
    cardHashPan?: string;
    ip: string;
    agent: string;
    inquiry?: InquiryType;
    settlementCode?: string | null;
    clientRefId?: string;
    refId?: string;
    code?: string;
    status: PaymentStatus;
    result_payment?: {
        Status: number;
        RefID: string;
    }
}

export enum TransactionType {
    INQUIRY = 'inquiry_app_results',
    DEPOSIT = 'transactions',
}

export interface IWallet extends IBase {
    user: Types.ObjectId;
    phone: string;
    amount: number;
    balance_before: number;
    balance_after: number;
    source: string;
    reference_type?: TransactionType;
    reference_id?: Types.ObjectId | string;
    reason?: string;
    refundId?: Types.ObjectId | string;
}

