'use client'

import dynamic from 'next/dynamic';
import {Suspense, useMemo} from 'react';
import {InquiryTypeEnum, ServiceComponentProps} from '@/lib/types';
import {Spinner} from "@/components/common/spinner";
import {componentMap} from "@/components/common/inquiry/map-types-to-forms";

interface DynamicFormProps extends ServiceComponentProps {
    componentKey: InquiryTypeEnum;
}

export function DynamicFormLoader({componentKey, ...props}: DynamicFormProps) {
    const LazyComponent = useMemo(
        () => dynamic(componentMap[componentKey], {ssr: false}),
        [componentKey]
    );

    return (
        <Suspense fallback={<div className='w-full h-[300px]'><Spinner className="text-primary" size="large"/></div>}>
            <LazyComponent {...props} />
        </Suspense>
    );
}