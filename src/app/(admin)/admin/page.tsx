// import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@/components/ui/card"
// import {ArrowUpRight, BarChart3, DollarSign, Package, Users} from "lucide-react"

export default async function AdminDashboard() {

    return (<></>)

    // return (
    //     <div className="h-full flex-1 flex-col space-y-8 p-8 md:flex">
    //         <div className="flex items-center justify-between space-y-2">
    //             <div>
    //                 <h2 className="text-2xl font-bold tracking-tight">داشبورد</h2>
    //                 <p className="text-muted-foreground">نمای کلی از عملکرد کسب و کار و خدمات شما.</p>
    //             </div>
    //         </div>
    //
    //         <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
    //             <Card>
    //                 <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
    //                     <CardTitle className="text-sm font-medium">درآمد کل</CardTitle>
    //                     <DollarSign className="h-4 w-4 text-muted-foreground"/>
    //                 </CardHeader>
    //                 <CardContent>
    //                     <div className="text-2xl font-bold">۴۵,۲۳۱.۸۹ تومان</div>
    //                     <p className="text-xs text-muted-foreground flex items-center">
    //                         <ArrowUpRight className="ml-1 h-3 w-3 text-green-500"/>
    //                         <span className="text-green-500">+۲۰.۱٪</span> نسبت به ماه قبل
    //                     </p>
    //                 </CardContent>
    //             </Card>
    //             <Card>
    //                 <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
    //                     <CardTitle className="text-sm font-medium">خدمات فعال</CardTitle>
    //                     <Package className="h-4 w-4 text-muted-foreground"/>
    //                 </CardHeader>
    //                 <CardContent>
    //                     <div className="text-2xl font-bold">+۱۲</div>
    //                     <p className="text-xs text-muted-foreground">۳ خدمت در این ماه اضافه شده</p>
    //                 </CardContent>
    //             </Card>
    //             <Card>
    //                 <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
    //                     <CardTitle className="text-sm font-medium">کاربران کل</CardTitle>
    //                     <Users className="h-4 w-4 text-muted-foreground"/>
    //                 </CardHeader>
    //                 <CardContent>
    //                     <div className="text-2xl font-bold">+۵۷۳</div>
    //                     <p className="text-xs text-muted-foreground flex items-center">
    //                         <ArrowUpRight className="ml-1 h-3 w-3 text-green-500"/>
    //                         <span className="text-green-500">+۱۲.۵٪</span> نسبت به ماه قبل
    //                     </p>
    //                 </CardContent>
    //             </Card>
    //             <Card>
    //                 <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
    //                     <CardTitle className="text-sm font-medium">اشتراک‌های فعال</CardTitle>
    //                     <BarChart3 className="h-4 w-4 text-muted-foreground"/>
    //                 </CardHeader>
    //                 <CardContent>
    //                     <div className="text-2xl font-bold">+۲۸۹</div>
    //                     <p className="text-xs text-muted-foreground">+۱۹ از هفته گذشته</p>
    //                 </CardContent>
    //             </Card>
    //         </div>
    //
    //         <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
    //             <Card className="col-span-4">
    //                 <CardHeader>
    //                     <CardTitle>نمای کلی درآمد</CardTitle>
    //                     <CardDescription>درآمد ماهانه برای سال جاری</CardDescription>
    //                 </CardHeader>
    //                 <CardContent className="h-[300px] flex items-center justify-center bg-muted/50 rounded-md">
    //                     <p className="text-muted-foreground">نمودار درآمد اینجا نمایش داده می‌شود</p>
    //                 </CardContent>
    //             </Card>
    //             <Card className="col-span-3">
    //                 <CardHeader>
    //                     <CardTitle>فعالیت‌های اخیر</CardTitle>
    //                     <CardDescription>آخرین فعالیت‌ها در پنل مدیریت شما</CardDescription>
    //                 </CardHeader>
    //                 <CardContent>
    //                     <div className="space-y-4">
    //                         {[1, 2, 3, 4, 5].map((i) => (
    //                             <div key={i} className="flex items-center gap-4">
    //                                 <div className="h-2 w-2 rounded-full bg-primary"></div>
    //                                 <div className="flex-1 space-y-1">
    //                                     <p className="text-sm font-medium leading-none">خدمت جدید اضافه شد</p>
    //                                     <p className="text-sm text-muted-foreground">خدمت نظافت ویژه اضافه شد</p>
    //                                 </div>
    //                                 <div className="text-sm text-muted-foreground">۲ ساعت پیش</div>
    //                             </div>
    //                         ))}
    //                     </div>
    //                 </CardContent>
    //             </Card>
    //         </div>
    //     </div>
    // )
}
