import {serviceList} from "@/lib/data/service-list";
import ServiceSettingsModel from "@/features/service/service.schema";
import {SOURCE_NAME} from "@/lib/constants";
import {CategorySlugEnum, InquiryTypeEnum, IService, ServiceType, SettingsValueTypeEnum} from "@/lib/types";
import ServiceModel from "@/features/service/service.schema";
import {ClientSession} from "mongoose";
import envConfig from "@/lib/config.env";

class ServiceSettings {

    async seedServices(): Promise<IService[]> {
        const servicesToInsert = serviceList.map(service => ({
            ...service,
            price: Number(service.price),
        }));

        return ServiceSettingsModel.insertMany(servicesToInsert);
    };

    async getServicesByCategory(category: CategorySlugEnum): Promise<IService[]> {
        return ServiceModel.find({category: category, value: SettingsValueTypeEnum.ACTIVE})
    }

    async create(service: ServiceType): Promise<IService> {
        const newService = new ServiceModel({
            ...service,
            price: Number(service.price),
        })
        return newService.save();
    }

    async getServices(): Promise<IService[]> {
        const env = envConfig()
        const isDevEnv = env.NODE_ENV === 'development';
        const query: any = {
            source: SOURCE_NAME,
        }
        query.env = isDevEnv ? {$in: ['development', 'production']} : 'production'

        return ServiceSettingsModel.find(query);
    }

    async findByKey(key: InquiryTypeEnum, session: ClientSession | null = null): Promise<IService | null> {
        return ServiceSettingsModel.findOne({
            key,
            source: SOURCE_NAME
        }).session(session)
    }


}

export default new ServiceSettings()