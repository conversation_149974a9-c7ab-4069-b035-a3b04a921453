import {Schema, model, models, Model} from 'mongoose';
import {IUserToken} from "@/lib/types";


const userTokenSchema = new Schema<IUserToken>(
    {
        access_token: {type: String, required: true},
        token: {type: String, required: true},
        phone: {type: String, required: true},
        status: {type: Number, default: 0},
        source: {
            type: String,
            required: true,
        },
        expiredIn: {type: Date, required: true},
        ip: {type: String, required: true},
        agent: {type: String, required: true},
    },
    {
        timestamps: true,
        collection: 'user_tokens',
    }
);


const UserTokenModel: Model<IUserToken> =
    (models.UserToken) ||
    model<IUserToken>("UserToken", userTokenSchema);

export default UserTokenModel;