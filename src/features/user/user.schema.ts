import {Schema, model, models, Model} from 'mongoose';
import {IUser} from "@/lib/types";


const UserSchema = new Schema<IUser>(
    {
        phone: {
            type: String,
            required: true
        },
        balance: {
            type: Number,
            default: 0
        },
        source: {
            type: String,
            required: true
        },
        admin: {
            type: Boolean,
        },
        lock: {
            type: Number,
            default: 0
        }
    },
    {
        timestamps: true,
        collection: 'users',
        optimisticConcurrency: true,
    }
);

const UserModel: Model<IUser> = models.User || model<IUser>('User', UserSchema);
export default UserModel;