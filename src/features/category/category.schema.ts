import {model, Model, models, Schema} from 'mongoose';
import {CategorySlugEnum, ICategory} from "@/lib/types";
import {SOURCE_NAME} from "@/lib/constants";

const CategorySchema = new Schema<ICategory>(
    {
        title: {
            type: String,
            required: true
        },
        path: {
            type: String,
            required: true
        },
        slug: {
            type: String,
            enum: Object.values(CategorySlugEnum),
            required: true
        },
        source: {
            type: String,
            enum: [SOURCE_NAME],
            default: SOURCE_NAME,
        },
        active: {
            type: "Boolean",
            required: true,
        },
        description: {
            type: String,
            required: true
        }
    },
    {
        timestamps: true,
        collection: 'categories',
        optimisticConcurrency: true,
    }
);

const CategoryModel: Model<ICategory> =
    (models.categories) ||
    model<ICategory>("categories", CategorySchema);

export default CategoryModel;

