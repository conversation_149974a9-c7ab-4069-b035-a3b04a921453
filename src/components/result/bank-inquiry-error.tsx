import {Card, CardContent, CardFooter} from "@/components/ui/card";
import CustomButton from "@/components/common/custom-button";
import {HOME_PATH} from "@/lib/routes";

type Props = {
    message: string
}

export default function BankInquiryError({message}: Props) {
    return (
        <Card className="w-full h-fit max-w-lg lg:max-w-2xl py-10 bg-white rounded-lg overflow-hidden">
            <CardContent className="px-6" dir='rtl'>
                <p className='text-neutral-700 text-center'>{message}</p>
            </CardContent>
            <div className="border-t border-gray-200 my-6"></div>
            <CardFooter className="px-6 pb-6">
                <CustomButton href={HOME_PATH} variants='green'>استعلام جدید</CustomButton>
            </CardFooter>
        </Card>
    );
}
