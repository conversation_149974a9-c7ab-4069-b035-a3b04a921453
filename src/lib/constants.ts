export const AUTHORIZATION = "Authorization"
export const RETURN_URL = '/returnUrl'
export const NATIONAL_CODE_MAX_LENGTH = 10;
export const PHONE_NUMBER_MAX_LENGTH = 11;
export const LOGIN_CODE_LENGTH = 5
export const SOURCE_NAME = 'migrofin'
export const SMS_TOKEN_IN_DEVELOPMENT = '12345'
export const JWT_TOKEN_TYPE = 'Bearer'
export const INQUIRY_PRICE = 1250
export const TRACKING_NUMBER_EXPIRE_TIME_IN_HOUR = 1
export const TRACKING_NUMBER_NAME = 'trackingNumber'
export const GHABZINO = 'NBG'
export const JIBIT = 'TBJ'
export const CAR_PLATE_LEFT = 0
export const CAR_PLATE_ALPHABET = 1
export const CAR_PLATE_MIDDLE = 2
export const CAR_PLATE_RIGHT = 3
export const MOTOR_PLATE_LEFT = 0
export const MOTOR_PLATE_RIGHT = 1
export const DISABLED = 'معلولین'
export const plateAlphabets = ["الف", "ب", "پ", "ت", "ث", "ج", "چ", "ح", "خ", "د", "ذ", "ر", "ز", "ژ", "س", "ش", "ص", "ض", "ط", "ظ", "ع", "غ", "ف", "ق", "ک", "گ", "ل", "م", "ن", "و", "ه", "ی", DISABLED, "S", "D"];
export const motorPlatePartsMaxLengths = [3, 5];
export const carPlatePartsMaxLengths = [2, 7, 3, 2];
export const X_SOURCE = "x-source"
export const IS_NEW = "isNew"
export const INQUIRY_TYPE = 'inquiryType'
export const INQUIRY_PATH = 'inquiryPath'
export const GCLID = 'gclid'
export const CONVERSION_X_API_KEY = 'x-api-key'