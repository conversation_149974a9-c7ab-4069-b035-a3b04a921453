'use client'

import {createContext, useCallback, useEffect, useMemo, useRef, useState} from "react";
import {usePathname, useRouter} from "next/navigation";
import {getUserInfo, logOut} from "@/features/auth/auth.action";
import status from "http-status";
import {ERROR_PATH, LOGIN_PATH, protectedRoutes} from "@/lib/routes";
import debounce from "lodash/debounce";
import {UserPayload} from "@/lib/types";

interface UserContextType {
    userData: UserPayload | null | undefined
    isLoggedIn: boolean
    reFetchUser: () => Promise<void>
    logoutUser: () => void

}

export const UserContext = createContext<UserContextType | undefined>(undefined);

export const UserProvider = ({children}: { children: React.ReactNode }) => {
    const [userData, setUserData] = useState<UserPayload | null | undefined>();
    const router = useRouter()
    const [loading, setLoading] = useState<boolean>(true);
    const pathname = usePathname(); // Get the current route
    const isLoggedIn = useMemo(() => {
        return !!userData
    }, [userData])
    const shouldFetch = useRef(true)

    const updateUserData = useCallback(async (currentPathname: string) => {
        if (currentPathname === ERROR_PATH) return;

        setLoading(true);
        for (let i = 0; i < 3; i++) {
            const actionResult = await getUserInfo();
            if (!actionResult.success) {
                if (actionResult.status === status.UNAUTHORIZED) {
                    await logOut();
                    setUserData(null);
                    if (protectedRoutes.includes(currentPathname)) {
                        router.push(LOGIN_PATH);
                    }
                    shouldFetch.current = false
                    break;
                }
                if (i === 2) {
                    shouldFetch.current = true
                    router.push(ERROR_PATH);
                    break;
                }
            } else {
                const {admin, balance, phone} = actionResult.data!;
                setUserData({
                    admin,
                    balance,
                    phone
                });
                shouldFetch.current = false
                break;
            }
        }
        setLoading(false);
    }, [router]);

    const debouncedUpdateUserData = useMemo(() => debounce(updateUserData, 1000), [updateUserData]);

    useEffect(() => {
        if (shouldFetch.current || isLoggedIn) {
            debouncedUpdateUserData(pathname);
        }
    }, [pathname, debouncedUpdateUserData, isLoggedIn]);

    useEffect(() => {
        let interval: NodeJS.Timeout | undefined
        if (isLoggedIn) {
            interval = setInterval(() => {
                debouncedUpdateUserData(pathname);
            }, 60000);
        }
        return () => {
            if (interval) {
                clearInterval(interval)
            }
        };
    }, [isLoggedIn, pathname, debouncedUpdateUserData]);

    const logoutUser = useCallback(() => {
        setUserData(null);
    }, []);

    return (
        <UserContext.Provider
            value={{userData, reFetchUser: () => updateUserData(pathname), isLoggedIn, logoutUser}}>
            {children}
        </UserContext.Provider>
    );
};
