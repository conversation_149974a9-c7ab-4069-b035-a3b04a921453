'use server'

import {ActionResult, AuthResponse, GetUserResponse, VerificationArgs} from "@/lib/types";
import {connectToDatabase} from "@/lib/mongodb";
import {loginFormSchema} from "@/lib/zod-schemas";
import tokenService from "@/lib/services/token.service";
import {BadRequestError, ForbiddenError, InternalServerError, NotFoundError, UnauthorizedError} from "@/lib/error";
import cookieService from "@/lib/services/cookie.service";
import {JWT_TOKEN_TYPE} from "@/lib/constants";
import {
    getAppSource,
    getCurrentUser,
    handleActionErrorResponse,
    handleZodValidationResult,
    remainingTimeInSeconds
} from "@/utils/helpers-server";
import envConfig from "@/lib/config.env";
import smsService from "@/lib/services/sms.service";
import mongoose from "mongoose";
import userTokenRepository from "@/features/user-token/user-token.repository";
import userRepository from "@/features/user/user.repository";


export async function authOTP(phone: string, bodyId: string): Promise<ActionResult<AuthResponse>> {
    try {
        await connectToDatabase()
        const mobileOnlySchema = loginFormSchema.pick({
            mobile: true,
        });
        const validationResult = mobileOnlySchema.safeParse({mobile: phone});
        handleZodValidationResult(validationResult);

        const source = await getAppSource()

        const userTokenFromDb = await userTokenRepository.findUserTokenForOtp({phone, source});

        if (userTokenFromDb) {
            return {
                success: true,
                data: {
                    message: 'موفق',
                    token: userTokenFromDb.access_token,
                    expire_time: remainingTimeInSeconds(userTokenFromDb.expiredIn)
                }
            }
        }

        const {otpToken, otpExpiresIn} = tokenService.generateOtpToken()

        const env = envConfig()

        if (env.NODE_ENV === "production") {
            const smsSent = await smsService.sendSms(otpToken, phone, bodyId);

            if (!smsSent) {
                throw new InternalServerError("ارسال پیامک ناموفق")
            }
        }

        const [{token}, {ip, userAgent}] = await Promise.all([
            tokenService.generateJwtToken(),
            userRepository.getUserAgent()
        ]);

        await userTokenRepository.createUserToken({
            phone,
            source,
            otpToken: otpToken,
            expiredIn: otpExpiresIn,
            accessToken: token,
            agent: userAgent,
            ip
        })

        return {
            success: true,
            data: {
                expire_time: remainingTimeInSeconds(otpExpiresIn),
                token: token,
                message: 'با موفقیت انجام شد'
            }
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}

export async function authVerification(args: VerificationArgs): Promise<ActionResult> {
    await connectToDatabase()
    const session = await mongoose.startSession();
    session.startTransaction();
    try {

        validateMobile(args.phone);

        const source = await getAppSource()

        const userTokenFromDb = await userTokenRepository.findUserTokenForOtp({phone: args.phone, source}, session)

        if (!userTokenFromDb) {
            throw new BadRequestError("اطلاعات ارسال شده نادرست است یا کد تایید قبلا منقضی شده")
        }

        const isLoginSuccess = userTokenFromDb.access_token === args.token && userTokenFromDb.token === args.code;

        if (!isLoginSuccess) {
            throw new BadRequestError("کد تأیید نامعتبر است یا قبلاً منقضی شده است.")
        }

        // otp token is one time use
        userTokenFromDb.status = 1

        let [userToLoggedIn] =
            await Promise.all([userRepository.findUserByPhone({
                source,
                phone: args.phone
            }, session), userTokenFromDb.save({session})])

        if (!userToLoggedIn) {
            userToLoggedIn = await userRepository.createUser({phone: args.phone, source}, session)
        } else {
            if (userToLoggedIn.lock === 1) {
                throw new ForbiddenError("حساب شما قفل می باشد.")
            }

            const balance = userToLoggedIn.balance
            if (typeof balance !== "number") {
                userToLoggedIn.balance = parseInt((userToLoggedIn.balance.toString()))
                await userToLoggedIn.save({session});
            }
        }

        const {token, expiresAt} = await tokenService.generateJwtToken({
            sub: userToLoggedIn._id.toString(),
            phone: userToLoggedIn.phone,
            admin: !!userToLoggedIn?.admin,
        });

        const tokenForCookie = `${JWT_TOKEN_TYPE} ${token}`

        await cookieService.setAuthorizationToken(tokenForCookie, expiresAt)

        await session.commitTransaction();
        return {
            success: true,
        }
    } catch (error: any) {
        await session.abortTransaction();
        return handleActionErrorResponse(error)
    } finally {
        await session.endSession();
    }
}

function validateMobile(mobile: string) {
    const mobileOnlySchema = loginFormSchema.pick({mobile: true});
    const validationResult = mobileOnlySchema.safeParse({mobile});
    handleZodValidationResult(validationResult);
}

// export async function getBalance(): Promise<ActionResult<GetUserResponse>> {
//     try {
//         await connectToDatabase()
//         const currentUser = await getCurrentUser();
//
//         if (!currentUser) {
//             throw new UnauthorizedError()
//         }
//
//         const walletFromDb = await walletService.getLatestEntry(currentUser.sub!)
//
//         return {
//             success: true,
//             data: {
//                 phone: walletFromDb ? walletFromDb.phone : currentUser.phone!,
//                 balance: walletFromDb ? walletFromDb.balance_after : 0,
//             },
//         }
//     } catch (error: any) {
//         return handleActionErrorResponse(error)
//     }
// }

export async function getUserInfo(): Promise<ActionResult<GetUserResponse>> {
    try {
        await connectToDatabase()
        const currentUser = await getCurrentUser();

        if (!currentUser) {
            throw new UnauthorizedError()
        }

        const source = await getAppSource()

        const userFromDb = await userRepository.findUserById({source, id: currentUser.sub!})

        if (!userFromDb) {
            throw new NotFoundError()
        }

        if (userFromDb.lock === 1) {
            throw new UnauthorizedError();
        }

        return {
            success: true,
            data: {
                phone: userFromDb.phone,
                admin: !!userFromDb?.admin,
                balance: userFromDb.balance,
            },
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}


export async function logOut(): Promise<ActionResult<GetUserResponse>> {
    try {
        await cookieService.deleteAuthorizationToken()
        return {
            success: true,
        }
    } catch (error: any) {
        return handleActionErrorResponse(error)
    }
}
