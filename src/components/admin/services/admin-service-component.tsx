'use client'

import {useState} from "react";
import {InquiryTypeEnum, ServiceType, SettingsValueTypeEnum} from "@/lib/types";
import {useService} from "@/lib/hooks/useService";
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, <PERSON><PERSON>Trigger} from "@/components/ui/tabs";
import {Search} from "lucide-react";
import {Input} from "@/components/ui/input";
import {Card, CardContent, CardFooter, CardHeader, CardTitle} from "@/components/ui/card";
import {Badge} from "@/components/ui/badge";
import {Switch} from "@/components/ui/switch";
import {Button} from "@/components/ui/button";
import {Label} from "@/components/ui/label";
import {formatWithComma} from "@/utils/helpers";
import {toggleServiceActive, updateServiceByKey} from "@/features/service/service.action";
import toast from "react-hot-toast";
import {ServiceUpdateSchema} from "@/lib/zod-schemas";
import {cn} from "@/lib/utils";

export default function AdminServiceComponent() {
    const {services} = useService()

    // Update the newService state to include category

    const [searchQuery, setSearchQuery] = useState("")

    // Add a state for category filter
    const [categoryFilter, setCategoryFilter] = useState<string>("all")
    const [providerFilter, setProviderFilter] = useState<string>("all")

    const handleToggleService = (value: SettingsValueTypeEnum) => {
        // setServices(services.map((service) => ({...service, value: value})))
    }

    const handleUpdatePrice = (id: string, price: string) => {
        // setServices(services.map((service) => (service.id === id ? {...service, price} : service)))
    }

    const handleUpdateTitle = (id: string, title: string) => {
        // setServices(services.map((service) => (service.id === id ? {...service, title} : service)))
    }

    // Update the handleUpdateCategory function
    const handleUpdateCategory = (id: string, category: string) => {
        // setServices(services.map((service) => (service.id === id ? {...service, category} : service)))
    }

    // const handleAddService = () => {
    //     if (newService.title && newService.key && newService.price) {
    //         setServices([
    //             ...services,
    //             {
    //                 id: Date.now().toString(),
    //                 ...newService,
    //             },
    //         ])
    //         setNewService({
    //             key: "",
    //             value: "",
    //             title: "",
    //             price: "",
    //             enabled: true,
    //             category: "",
    //         })
    //     }
    // }

    const uniqueProviders = Array.from(new Set(services.map((service) => service.provider).filter(Boolean)))

    // Update the filteredServices to include category filtering
    const filteredServices = services.filter(
        (service) =>
            (service.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                service.key.toLowerCase().includes(searchQuery.toLowerCase()) ||
                service.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
                (service.provider && service.provider.toLowerCase().includes(searchQuery.toLowerCase()))) &&
            (categoryFilter === "all" || service.category === categoryFilter) &&
            (providerFilter === "all" || service.provider === providerFilter),
    )

    const activeServices = services.filter((service) => service.value === 'ACTIVE').length
    // const totalRevenue = services
    //     .filter((service) => service.value === 'ACTIVE')
    //     .reduce((sum, service) => sum + Number.parseInt(service.price.replace(/[^\d]/g, "")), 0)

    // Add a function to get unique categories
    const uniqueCategories = Array.from(new Set(services.map((service) => service.category))).filter(Boolean)

    return (
        <div className=" h-full flex-1 flex-col space-y-8 p-4 md:p-8 md:flex">
            <div className="flex items-center justify-between space-y-2">
                <div>
                    <h2 className="text-2xl font-bold tracking-tight">مدیریت خدمات</h2>
                    <p className="text-muted-foreground mt-2">خدمات، قیمت‌گذاری و دسترسی خود را مدیریت کنید.</p>
                </div>

            </div>
            <Tabs defaultValue="all" className="space-y-4">
                <div
                    className="flex bg-white rounded-xl px-2 justify-between flex-col md:flex-row gap-4">
                    <div className="flex flex-col sm:flex-row gap-4">
                        <TabsList>
                            <TabsTrigger value="all">همه خدمات</TabsTrigger>
                            <TabsTrigger value="active">فعال</TabsTrigger>
                            <TabsTrigger value="inactive">غیرفعال</TabsTrigger>
                        </TabsList>

                        <div className="flex items-center space-x-2">
                            <select
                                className="h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
                                value={categoryFilter}
                                onChange={(e) => setCategoryFilter(e.target.value)}
                            >
                                <option value="all">همه دسته‌بندی‌ها</option>
                                {uniqueCategories.map((category) => (
                                    <option key={category} value={category}>
                                        {category}
                                    </option>
                                ))}
                            </select>
                            <select
                                className="h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
                                value={providerFilter}
                                onChange={(e) => setProviderFilter(e.target.value)}
                            >
                                <option value="all">همه ارائه‌دهنده‌ها</option>
                                {uniqueProviders.map((provider) => (
                                    <option key={provider} value={provider}>
                                        {provider}
                                    </option>
                                ))}
                            </select>
                        </div>
                    </div>

                    <div className="relative">
                        <Search className="absolute right-2.5 top-2.5 h-4 w-4 text-muted-foreground"/>
                        <Input
                            type="search"
                            placeholder="جستجوی خدمات..."
                            className="w-full pr-8 md:w-[300px]"
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                        />
                    </div>
                </div>
                <TabsContent value="all" className="space-y-4">
                    <div className="grid  gap-4 md:grid-cols-3 lg:grid-cols-4">
                        <Card>
                            <CardHeader
                                className="flex  text-center flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">کل خدمات</CardTitle>
                                <div className="h-4 w-4 text-muted-foreground"/>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{services.length}</div>
                                <p className="text-sm text-muted-foreground">{activeServices} خدمت فعال</p>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">خدمات فعال</CardTitle>
                                <div className="h-4 w-4 text-muted-foreground"/>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{activeServices}</div>
                                <p className="text-sm text-muted-foreground">
                                    {((activeServices / services.length) * 100).toFixed(0)}٪ از کل خدمات
                                </p>
                            </CardContent>
                        </Card>
                    </div>
                    <div className="grid  rounded-xl gap-4">
                        {filteredServices.map((service) => (
                            <ServiceCard
                                key={service.key}
                                service={service}
                                onToggle={() => handleToggleService(service.value)}
                                onUpdatePrice={handleUpdatePrice}
                                onUpdateTitle={handleUpdateTitle}
                                onUpdateCategory={handleUpdateCategory}
                            />
                        ))}
                    </div>
                </TabsContent>
                <TabsContent value="active" className="space-y-4">
                    <div className="grid gap-4">
                        {filteredServices
                            .filter((service) => service.value === 'ACTIVE')
                            .map((service) => (
                                <ServiceCard
                                    key={service.key}
                                    service={service}
                                    onToggle={() => handleToggleService(service.value)}
                                    onUpdatePrice={handleUpdatePrice}
                                    onUpdateTitle={handleUpdateTitle}
                                    onUpdateCategory={handleUpdateCategory}
                                />
                            ))}
                    </div>
                </TabsContent>
                <TabsContent value="inactive" className="space-y-4">
                    <div className="grid gap-4">
                        {filteredServices
                            .filter((service) => service.value === 'DEACTIVE')
                            .map((service) => (
                                <ServiceCard
                                    key={service.key}
                                    service={service}
                                    onToggle={() => handleToggleService(service.value)}
                                    onUpdatePrice={handleUpdatePrice}
                                    onUpdateTitle={handleUpdateTitle}
                                    onUpdateCategory={handleUpdateCategory}
                                />
                            ))}
                    </div>
                </TabsContent>

            </Tabs>
        </div>
    )
}

// Update the ServiceCard props interface
interface ServiceCardProps {
    service: ServiceType
    onToggle: (id: string) => void
    onUpdatePrice: (id: string, price: string) => void
    onUpdateTitle: (id: string, title: string) => void
    onUpdateCategory: (id: string, category: string) => void
}

// Update the ServiceCard component to include category and RTL support
function ServiceCard({service, onToggle, onUpdatePrice, onUpdateTitle, onUpdateCategory}: ServiceCardProps) {
    const [isEditing, setIsEditing] = useState(false)
    const [editedTitle, setEditedTitle] = useState(service.title)
    const [editedPrice, setEditedPrice] = useState(service.price)
    const [editedValidPeriod, setEditedValidPeriod] = useState(service.validPeriod)
    const [isPending, setIsPending] = useState(false)
    const {refetchServices} = useService()
    // const isDirty = service.title !== editedTitle || service.price !== editedPrice || service.validPeriod !== editedValidPeriod

    const handleSave = async (key: InquiryTypeEnum, version: number) => {
        setIsPending(true)
        const price = Number(editedPrice)
        const zodResult = ServiceUpdateSchema.safeParse({title: editedTitle, price, validPeriod: editedValidPeriod})

        if (!zodResult.success) {
            const errors = zodResult.error.errors.map((e: { message: string }) => e.message);
            toast.error(errors[0])
            setIsPending(false)
            return
        }

        const actionResult = await updateServiceByKey(
            key,
            {
                title: editedTitle, price,
                expectedVersion: version,
                validPeriod: editedValidPeriod,
            })

        if (!actionResult.success) {
            toast.error(actionResult.message!);
        } else {
            await refetchServices()
            setIsEditing(false)
        }
        setIsPending(false)
    }

    // Get a color for the category badge
    const getCategoryColor = (category: string) => {
        const colors = {
            مسکونی: "bg-blue-100 text-blue-800 border-blue-200",
            تجاری: "bg-purple-100 text-purple-800 border-purple-200",
            "کسب و کار": "bg-amber-100 text-amber-800 border-amber-200",
        }
        return colors[category as keyof typeof colors] || "bg-gray-100 text-gray-800 border-gray-200"
    }

    async function handleToggleServiceActive(key: InquiryTypeEnum) {
        setIsPending(true)
        const actionResult = await toggleServiceActive(key)
        if (!actionResult.success) {
            toast.error(actionResult.message!)
        } else {
            await refetchServices()
        }

        setIsPending(false)
    }

    return (
        <Card className={`${service.value === 'DEACTIVE' ? "opacity-75" : ""}`}>
            <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                    <div className="flex flex-wrap items-center gap-2">
                        {/*<Badge variant={service.value === 'ACTIVE' ? "default" : "outline"} className="capitalize">*/}
                        {/*    {service.key}*/}
                        {/*</Badge>*/}
                        {service.category && (
                            <span
                                className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(service.category)}`}>
                {service.category}
              </span>
                        )}
                        {service.value === 'ACTIVE' ? (
                            <Badge variant='outline' className="bg-green-500 text-white">
                                فعال
                            </Badge>
                        ) : (
                            <Badge variant="destructive" className="bg-gray-400">
                                غیرفعال
                            </Badge>
                        )}
                    </div>
                    <div className="flex items-center space-x-2">
                        <Button variant="ghost" size="sm" onClick={() => setIsEditing(!isEditing)}
                                className="ml-2 cursor-pointer">
                            {isEditing ? "انصراف" : "ویرایش"}
                        </Button>
                        <Switch
                            checked={service.value === 'ACTIVE'}
                            onCheckedChange={() => handleToggleServiceActive(service.key)}
                            className={cn('left-direction w-10 h-5 cursor-pointer data-[state=checked]:bg-blue-600')}
                            disabled={isPending}
                        />
                    </div>
                </div>
            </CardHeader>
            <CardContent>
                {isEditing ? (
                    <div className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor={`title-${service.key}`}>عنوان خدمت</Label>
                            <Input id={`title-${service.key}`} value={editedTitle}
                                   onChange={(e) => setEditedTitle(e.target.value)}/>
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor={`expire-${service.key}`}>زمان اعتبار (ساعت)</Label>
                            <Input
                                id={`expire-${service.key}`}
                                value={editedValidPeriod}
                                maxLength={3}
                                inputMode="numeric"
                                pattern="[0-9]*"
                                onChange={(e) => {
                                    const value = e.target.value;
                                    if (/^\d*$/.test(value)) {
                                        setEditedValidPeriod(Number(value));
                                    }
                                }}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor={`price-${service.key}`}>قیمت (تومان)</Label>
                            <Input
                                id={`price-${service.key}`}
                                value={editedPrice}
                                inputMode="numeric"
                                pattern="[0-9]*"
                                onChange={(e) => {
                                    const value = e.target.value;
                                    if (/^\d*$/.test(value)) {
                                        setEditedPrice(value);
                                    }
                                }}
                            />
                        </div>
                    </div>
                ) : (
                    <div className="space-y-2">
                        <h3 className="font-semibold font-mono text-xl">{service.title}</h3>
                        <div className="font-medium text-lg">{formatWithComma(service.price)} تومان</div>
                        <div className="text-sm text-muted-foreground flex items-center">
                            <span>زمان اعتبار: {service.validPeriod}</span><span className='mr-1'>ساعت</span>
                        </div>
                        <div className="text-sm text-muted-foreground flex items-center">
                            <span>سیاست استعلام: {service.inquiryPolicy}</span>
                        </div>
                        <div className="text-sm text-muted-foreground flex items-center">
                            <span>ارائه‌دهنده: {service.provider}</span>
                        </div>
                    </div>
                )}
            </CardContent>
            {isEditing && (
                <CardFooter className='mt-4'>
                    <Button
                        onClick={() => handleSave(service.key, service.version!)}
                        disabled={isPending || isPending}
                        className='cursor-pointer'
                    >ذخیره تغییرات</Button>
                </CardFooter>
            )}
        </Card>
    )
}
