import {ReactNode} from "react";
import {cn} from "@/lib/utils";

type Props = {
    children: ReactNode
    alignVertical?: boolean
}

export default function Container({children, alignVertical}: Props) {
    return (
        <div className={cn('flex min-h-[calc(100vh-140px)] justify-center px-2 py-4 md:px-4 md:my-10', {
            'md:items-center': alignVertical,
        })}>
            {children}
        </div>
    );
}
