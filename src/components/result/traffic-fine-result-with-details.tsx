'use client'

import {useState} from "react";
import {cn} from "@/lib/utils";
import ChoiceWrapper from "@/components/common/choice-wrapper";
import CustomButton from "@/components/common/custom-button";
import {GhabzinoTrafficDetails} from "@/lib/types";
import {formatWithComma} from "@/utils/helpers";

type Props = {
    isCollapse?: boolean;
    detail: GhabzinoTrafficDetails
}


export default function TrafficFineResultWithDetails({detail, isCollapse = true}: Props) {
    const [collapse, setCollapse] = useState(isCollapse)

    return (
        <ChoiceWrapper
            backgroundColor='#F9FAFB'
            borderColor={collapse ? "#EEEEEE" : "#CCCACA"}
            className={collapse ? 'border-solid !border' : ''}
        >
            <div
                onClick={() => setCollapse(!collapse)}
                className='relative  w-full cursor-pointer'>
                <div className={cn('w-full p-2 pt-5 pb-10 h-[360px] overflow-hidden duration-200 transition-all', {
                    "h-[115px]": collapse
                })}>
                    {/*<div className='absolute bottom-[-7px] left-[-13px]'>*/}
                    {/*    {collapse ? <SubtractBorderIcon width={100} height={100}/> :*/}
                    {/*        <SubtractIcon width={100} height={100}/>}*/}
                    {/*</div>*/}
                    {/*<div className='absolute bottom-[-13px] left-[22px] cursor-pointer'*/}
                    {/*     onClick={() => setCollapse((prev) => !prev)}>*/}
                    {/*    {*/}
                    {/*        !collapse ? (<ResultDetailsUpIcon width={30} height={30}/>) : (<ResultDetailsLeftIcon/>)*/}
                    {/*    }*/}
                    {/*</div>*/}
                    <div className='w-full flex justify-between items-center gap-x-1'>
                    <span>
                        <p className='text-[#000000]'>{formatWithComma(detail.Amount.toString())} <span
                            className='text-xs'>تومان</span></p>
                        <p className='text-xs mt-1'>{detail.Type}</p>
                    </span>
                        {/*<CameraIcon height={40} width={40}/>*/}
                    </div>
                    {collapse && (<div className='mt-2 flex items-center gap-1 text-[#9DA5B0] text-xs'>
                        <span>تاریخ وقوع:</span>
                        <span>{detail.DateTime}</span>
                    </div>)}
                    <div className={cn('mt-5 flex flex-col items-center gap-5', {
                        "hidden": collapse,
                    })}>
                        <div className='w-full flex justify-between items-center text-[#596068] text-xs'>
                            <span>تاریخ وقوع:</span>
                            <span>{detail.DateTime}</span>
                        </div>
                        {/*<div className='w-full flex justify-between items-center text-[#596068]'>*/}
                        {/*    <span className='text-xs'>روش ثبت:</span>*/}
                        {/*    <span className='text-xs'>{detail.}</span>*/}
                        {/*</div>*/}
                        <div className='w-full flex justify-between items-center text-[#596068]'>
                            <span className='text-xs'>محل وقوع:</span>
                            <span className='text-xs'>{detail.Location}</span>
                        </div>
                        <div className='w-full flex justify-between items-center text-[#596068]'>
                            <span className='text-xs'>شناسه قبض:</span>
                            <span className='text-xs'>{detail.BillID}</span>
                        </div>
                        <div className='w-full flex justify-between items-center text-[#596068]'>
                            <span className='text-xs'>شناسه پرداخت:</span>
                            <span className='text-xs'>{detail.PaymentID}</span>
                        </div>
                        <div className='w-full flex justify-between items-center text-[#596068]'>
                            <span className='text-xs'>شماره ریال جریمه:</span>
                            <span className='text-xs'>{detail.SerialNumber}</span>
                        </div>
                        <CustomButton
                            variants='blue'
                            onClick={(e) => {
                                e.stopPropagation()
                                window.open(detail.PaymentUrl, "_blank");
                            }}>پرداخت</CustomButton>
                    </div>

                </div>

            </div>
        </ChoiceWrapper>
    );
}
