'use client'

import {useState} from "react";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription, Di<PERSON><PERSON>ooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger
} from "@/components/ui/dialog";
import CustomButton from "@/components/common/custom-button";
import {Button} from "@/components/ui/button";
import {Label} from "@/components/ui/label";
import {Plus, Search} from "lucide-react";
import {cn} from "@/lib/utils";
import {useRouter} from "next/navigation";

type Props = {
    newInquiryUrl: string;
    onReInquirySelected: () => void;
    isLoading: boolean;
}

export default function ReInquiryModal({newInquiryUrl, onReInquirySelected, isLoading}: Props) {
    const [open, setOpen] = useState(false)
    const [inquiryType, setInquiryType] = useState<'current' | 'new' | undefined>()
    const router = useRouter()

    const handleSubmit = () => {
        if (isLoading) return;
        // Handle form submission logic here
        setOpen(false)
        if (inquiryType == "new") {
            router.push(newInquiryUrl)
        } else if (inquiryType == "current") {
            onReInquirySelected?.()
        }
    }

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <CustomButton
                    loading={isLoading}
                    disabled={isLoading}
                    variants='blue'
                    className='mt-2 py-4'>استعلام
                    جدید</CustomButton>
            </DialogTrigger>
            <DialogContent
                className="sm:max-w-md [&_button[data-dialog-close]]:!right-auto [&_button[data-dialog-close]]:!left-10"
                dir="rtl">
                <DialogHeader className=''>
                    <DialogTitle className="text-xl text-right">استعلام پلاک خودرو</DialogTitle>
                    <DialogDescription className="text-right">
                        نوع استعلامی که می‌خواهید انجام دهید را انتخاب کنید.
                    </DialogDescription>
                </DialogHeader>
                <div className="py-4">
                    <div className="flex flex-col gap-6">
                        <div
                            className={cn(`flex items-start space-x-reverse space-x-4 rounded-lg border p-4 hover:bg-slate-50 cursor-pointer`, {
                                'border-2 border-green-600': inquiryType === 'current'
                            })}
                            onClick={() => setInquiryType('current')}
                        >
                            <div className="flex-1">
                                <div className="flex items-center space-x-reverse space-x-2">
                                    <Label htmlFor="current" className="text-base font-medium">
                                        استعلام مجدد پلاک فعلی
                                    </Label>
                                    <span className='text-xs text-green-800 mr-1'>(با کسر هزینه)</span>
                                </div>
                                <p className="mt-2 text-sm text-slate-600">می خواهم از این پلاک استعلام مجدد بگیرم
                                </p>
                            </div>
                            <div className="mt-1 rounded-full bg-slate-100 p-2">
                                <Search className="h-5 w-5 text-emerald-600"/>
                            </div>
                        </div>

                        <div
                            className={cn(`flex items-start space-x-reverse space-x-4 rounded-lg border p-4 hover:bg-slate-50 cursor-pointer`, {
                                'border-2 border-green-600': inquiryType === 'new'
                            })}
                            onClick={() => setInquiryType('new')}
                        >
                            <div className="flex-1">
                                <div className="flex items-center space-x-reverse space-x-2">
                                    <Label htmlFor="new" className="text-base font-medium">
                                        استعلام پلاک جدید
                                    </Label>
                                </div>
                                <p className="mt-2 text-sm text-slate-600">
                                    می خواهم برای پلاک جدیدی استعلام بگیرم
                                </p>
                            </div>
                            <div className="mt-1 rounded-full bg-slate-100 p-2">
                                <Plus className="h-5 w-5 text-emerald-600"/>
                            </div>
                        </div>
                    </div>
                </div>
                <DialogFooter className="flex-row justify-end">
                    <Button
                        type="submit"
                        onClick={handleSubmit}
                        disabled={!inquiryType}
                        className="bg-emerald-600 hover:bg-emerald-700 text-white cursor-pointer"
                    >
                        ادامه
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}
