import {
    BankInquiryType, GhabzinoBankResult, GhabzinoBankType,
    GhabzinoResponse,
    GhabzinoResultType,
    GhabzinoSayadiInquiryResult,
    IInquiryResult,
    InquiryTypeEnum,
    JibitResultType, VehiclePaymentDetails
} from "@/lib/types";


function convertToGhabzinoBankType(data: IInquiryResult): GhabzinoBankType {
    const inquiryResult = (data?.inquiryResult as GhabzinoResponse<GhabzinoBankResult>)
    const params = inquiryResult.Parameters

    return {
        bankName: params?.BankName || data?.bank,
        depositNumber: data?.depositNumber,
        extraInfo: params?.ExtraInfo ? JSON.parse(params.ExtraInfo) : undefined,
        imageUrl: params?.ImageUrl,
        shebaNumber: params?.ShebaNumber,
        whiteImageUrl: params?.WhiteImageUrl,
        bankShowName: params?.BankShowName,
        colorCode: params?.ColorCode,
        inquiryType: data.inquiryType,
        cardNumber: data?.cardNumber,
        description: params ? undefined : inquiryResult.Status?.Description,
    }
}

function convertToJibitResultType(data: IInquiryResult): JibitResultType {
    const inquiryResult = (data?.inquiryResult as BankInquiryType)

    return {
        bank: inquiryResult?.ibanInfo?.bank || inquiryResult?.depositInfo?.bank || inquiryResult?.cardInfo?.bank,
        // status: (data?.inquiryType === InquiryTypeEnum.Jibit_CardInquiry || data?.inquiryType === InquiryTypeEnum.Jibit_IbanInquiry) ? inquiryResult?.ibanInfo?.status : undefined,
        iban: inquiryResult?.ibanInfo?.iban,
        depositNumber: inquiryResult?.ibanInfo?.depositNumber || inquiryResult?.depositInfo?.depositNumber || inquiryResult?.cardInfo?.depositNumber,
        owners: inquiryResult?.ibanInfo?.owners ? inquiryResult?.ibanInfo?.owners.map((item) => `${item.firstName} ${item.lastName}`) : [],
        inquiryType: data.inquiryType,
        cardNumber: data?.cardNumber,
        ownerName: inquiryResult?.cardInfo?.ownerName
    }
}

function convertToGhabzinoResultType(data: IInquiryResult): GhabzinoResultType {
    const inquiryResult = (data?.inquiryResult as GhabzinoResponse<GhabzinoSayadiInquiryResult>);
    return {
        inquiryType: data.inquiryType,
        sayadiResult: inquiryResult?.Parameters,
        description: inquiryResult?.Status?.Description,
        sayadId: data?.sayadId
    }
}

function convertToVehiclePaymentDetails(result: IInquiryResult, isForHistory = false
): Partial<VehiclePaymentDetails> {
    const inquiryResult = result?.inquiryResult as GhabzinoResponse<VehiclePaymentDetails>
    const params = result.inquiryParams
    const parameters = inquiryResult.Parameters

    let returnResult: Partial<VehiclePaymentDetails> = {}
    if (params) {
        returnResult = {
            Details: isForHistory ? [] : (returnResult?.Details ?? []),
            plaque: {
                middle: params?.Mid,
                alphabet: params?.Alphabet,
                right: params.Right,
                left: params.Left,
            },
            isMotor: params.isMotor,
            withDetails: params.withDetails,
            inquiryType: params.inquiryType,
            phoneNumber: result?.inquiry_mobileNumber || undefined,
            nationalId: result?.inquiry_nationalId || undefined,
            inquiryUrl: result?.inquiryUrl
        }
    }

    if (parameters) {
        returnResult = {
            ...parameters,
            ...returnResult,
        }
    } else {
        returnResult = {
            ...returnResult,
            description: inquiryResult?.Status?.Description,
        }
    }

    return returnResult;
}

export function convertInquiryTypes(data: IInquiryResult, isForHistory = false) {
    if (data.inquiryType === InquiryTypeEnum.Jibit_CardToDeposit ||
        data.inquiryType === InquiryTypeEnum.Jibit_CardToIban ||
        data.inquiryType === InquiryTypeEnum.Jibit_DepositToIban ||
        data.inquiryType === InquiryTypeEnum.Jibit_CardInquiry ||
        data.inquiryType === InquiryTypeEnum.Jibit_IbanInquiry) {
        return convertToJibitResultType(data)
    } else if (data.inquiryType === InquiryTypeEnum.Ghabzino_SayadCheckInquiry) {
        return convertToGhabzinoResultType(data)
    } else if (data.inquiryType === InquiryTypeEnum.Ghanzino_KhalafiMotor ||
        data.inquiryType === InquiryTypeEnum.Ghanzino_KhalafiKhodro) {
        return convertToVehiclePaymentDetails(data, isForHistory)
    } else if (data.inquiryType === InquiryTypeEnum.Ghanzino_DepositToIban ||
        data.inquiryType === InquiryTypeEnum.Ghanzino_CardToIban) {
        return convertToGhabzinoBankType(data)
    }
}