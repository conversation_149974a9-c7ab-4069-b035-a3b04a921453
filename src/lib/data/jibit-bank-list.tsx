import "iranianbanklogos/dist/ibl.css";
import {BankInfoType} from "@/lib/types";

export const banks: BankInfoType[] = [
    // First image (bank1.png)
    {
        bankId: "MARKAZI",
        bankName: "Central Bank of the Islamic Republic of Iran",
        bankNameFarsi: "بانک مرکزی جمهوری اسلامی ایران",
        icon: undefined,
        iconSmall: undefined,
        ghabzino: true
    },
    {
        bankId: "SANAT_VA_MADAN",
        bankName: "Bank of Industry & Mine",
        bankNameFarsi: "بانک صنعت و معدن",
        icon: <i className="ibl64 ibl-bim"></i>,
        iconSmall: <i className="ibl32 ibl-bim"></i>,
        ghabzino: true
    },
    {
        bankId: "MELLAT",
        bankName: "Bank Mellat",
        bankNameFarsi: "بانک ملت",
        icon: <i className="ibl64 ibl-mellat"></i>,
        iconSmall: <i className="ibl32 ibl-mellat"></i>,
        ghabzino: true
    },
    {
        bankId: "REFAH",
        bankName: "Refah K. Bank",
        bankNameFarsi: "بانک رفاه کارگران",
        icon: <i className="ibl64 ibl-rb"></i>,
        iconSmall: <i className="ibl32 ibl-rb"></i>,
        ghabzino: true
    },
    {
        bankId: "MASKAN",
        bankName: "Bank Maskan",
        bankNameFarsi: "بانک مسکن",
        icon: <i className="ibl64 ibl-maskan"></i>,
        iconSmall: <i className="ibl32 ibl-maskan"></i>,
        ghabzino: true
    },
    {
        bankId: "SEPAH",
        bankName: "Bank Sepah",
        bankNameFarsi: "بانک سپه",
        icon: <i className="ibl64 ibl-sepah"></i>,
        iconSmall: <i className="ibl32 ibl-sepah"></i>,
        ghabzino: true
    },
    {
        bankId: "KESHAVARZI",
        bankName: "Bank Keshavarzi Iran",
        bankNameFarsi: "بانک کشاورزی ایران",
        icon: <i className="ibl64 ibl-bki"></i>,
        iconSmall: <i className="ibl32 ibl-bki"></i>,
        ghabzino: true
    },
    {
        bankId: "MELLI",
        bankName: "Bank Melli Iran",
        bankNameFarsi: "بانک ملی ایران",
        icon: <i className="ibl64 ibl-bmi"></i>,
        iconSmall: <i className="ibl32 ibl-bmi"></i>,
        ghabzino: true
    },
    {
        bankId: "TEJARAT",
        bankName: "Tejarat Bank",
        bankNameFarsi: "بانک تجارت",
        icon: <i className="ibl64 ibl-tejarat"></i>,
        iconSmall: <i className="ibl32 ibl-tejarat"></i>,
        ghabzino: true
    },
    {
        bankId: "SADERAT",
        bankName: "Bank Saderat Iran",
        bankNameFarsi: "بانک صادرات ایران",
        icon: <i className="ibl64 ibl-bsi"></i>,
        iconSmall: <i className="ibl32 ibl-bsi"></i>,
        ghabzino: true
    },
    {
        bankId: "TOSEAH_SADERAT",
        bankName: "Export Development Bank of Iran",
        bankNameFarsi: "بانک توسعه صادرات ایران",
        icon: <i className="ibl64 ibl-edbi"></i>,
        iconSmall: <i className="ibl32 ibl-edbi"></i>,
        ghabzino: true
    },
    {
        bankId: "POST",
        bankName: "Post Bank Iran",
        bankNameFarsi: "پست بانک ایران",
        icon: <i className="ibl64 ibl-post"></i>,
        iconSmall: <i className="ibl32 ibl-post"></i>,
        ghabzino: true
    },
    {
        bankId: "TOSEAH_TAAVON",
        bankName: "Tose'e Ta'avon Bank",
        bankNameFarsi: "بانک توسعه تعاون",
        icon: <i className="ibl64 ibl-tt"></i>,
        iconSmall: <i className="ibl32 ibl-tt"></i>,
        ghabzino: true
    },
    {
        bankId: "KARAFARIN",
        bankName: "Karafarin Bank",
        bankNameFarsi: "بانک کارآفرین",
        icon: <i className="ibl64 ibl-kar"></i>,
        iconSmall: <i className="ibl32 ibl-kar"></i>,
        ghabzino: true
    },
    {
        bankId: "PARSIAN",
        bankName: "Parsian Bank",
        bankNameFarsi: "بانک پارسیان",
        icon: <i className="ibl64 ibl-parsian"></i>,
        iconSmall: <i className="ibl32 ibl-parsian"></i>,
        ghabzino: true
    },
    {
        bankId: "EGHTESAD_NOVIN",
        bankName: "Bank Eghtesad Novin",
        bankNameFarsi: "بانک اقتصاد نوین",
        icon: <i className="ibl64 ibl-en"></i>,
        iconSmall: <i className="ibl32 ibl-en"></i>,
        ghabzino: true
    },
    {
        bankId: "SAMAN",
        bankName: "Saman Bank",
        bankNameFarsi: "بانک سامان",
        icon: <i className="ibl64 ibl-sb"></i>,
        iconSmall: <i className="ibl32 ibl-sb"></i>,
        ghabzino: true
    },
    {
        bankId: "PASARGAD",
        bankName: "Bank Pasargad",
        bankNameFarsi: "بانک پاسارگاد",
        icon: <i className="ibl64 ibl-bpi"></i>,
        iconSmall: <i className="ibl32 ibl-bpi"></i>,
        ghabzino: true
    },
    {
        bankId: "SARMAYEH",
        bankName: "Sarmayeh Bank",
        bankNameFarsi: "بانک سرمایه",
        icon: <i className="ibl64 ibl-sarmayeh"></i>,
        iconSmall: <i className="ibl32 ibl-sarmayeh"></i>,
        ghabzino: true
    },
    {
        bankId: "SINA",
        bankName: "Sina Bank",
        bankNameFarsi: "بانک سینا",
        icon: <i className="ibl64 ibl-sina"></i>,
        iconSmall: <i className="ibl32 ibl-sina"></i>,
        ghabzino: true
    },
    {
        bankId: "MEHR_IRAN",
        bankName: "Gharzolhasane Mehr Iran Bank",
        bankNameFarsi: "بانک قرض‌الحسنه مهر ایران",
        icon: <i className="ibl64 ibl-miran"></i>,
        iconSmall: <i className="ibl32 ibl-miran"></i>,
        ghabzino: true
    },
    {
        bankId: "SHAHR",
        bankName: "Shahr Bank",
        bankNameFarsi: "بانک شهر",
        icon: <i className="ibl64 ibl-shahr"></i>,
        iconSmall: <i className="ibl32 ibl-shahr"></i>,
        ghabzino: true
    },
    {
        bankId: "AYANDEH",
        bankName: "Ayandeh Bank",
        bankNameFarsi: "بانک آینده",
        icon: <i className="ibl64 ibl-ba"></i>,
        iconSmall: <i className="ibl32 ibl-ba"></i>,
        ghabzino: true
    },
    {
        bankId: "GARDESHGARI",
        bankName: "Tourism Bank",
        bankNameFarsi: "بانک گردشگری",
        icon: <i className="ibl64 ibl-tourism"></i>,
        iconSmall: <i className="ibl32 ibl-tourism"></i>,
        ghabzino: true
    },
    {
        bankId: "DAY",
        bankName: "Day Bank",
        bankNameFarsi: "بانک دی",
        icon: <i className="ibl64 ibl-day"></i>,
        iconSmall: <i className="ibl32 ibl-day"></i>,
        ghabzino: true
    },
    {
        bankId: "IRANZAMIN",
        bankName: "Iran Zamin Bank",
        bankNameFarsi: "بانک ایران زمین",
        icon: <i className="ibl64 ibl-iz"></i>,
        iconSmall: <i className="ibl32 ibl-iz"></i>,
        ghabzino: true
    },
    {
        bankId: "RESALAT",
        bankName: "Resalat Gharzolhasane Bank",
        bankNameFarsi: "بانک قرض‌الحسنه رسالت",
        icon: <i className="ibl64 ibl-resalat"></i>,
        iconSmall: <i className="ibl32 ibl-resalat"></i>,
        ghabzino: true
    },
    {
        bankId: "MELAL",
        bankName: "Melal Credit Institution",
        bankNameFarsi: "موسسه اعتباری ملل",
        icon: <i className="ibl64 ibl-melal"></i>,
        iconSmall: <i className="ibl32 ibl-melal"></i>,
        ghabzino: true
    },
    {
        bankId: "KHAVARMIANEH",
        bankName: "Middle East Bank",
        bankNameFarsi: "بانک خاورمیانه",
        icon: <i className="ibl64 ibl-me"></i>,
        iconSmall: <i className="ibl32 ibl-me"></i>,
        ghabzino: true
    },
    {
        bankId: "NOOR",
        bankName: "Noor Credit Institution",
        bankNameFarsi: "موسسه اعتباری نور",
        icon: undefined,
        iconSmall: undefined,
        ghabzino: false
    },
    {
        bankId: "IRAN_VENEZUELA",
        bankName: "Iran-Venezuela BiNational Bank",
        bankNameFarsi: "بانک مشترک ایران-ونزوئلا",
        icon: <i className="ibl64 ibl-ivbb"></i>,
        iconSmall: <i className="ibl32 ibl-ivbb"></i>,
        ghabzino: false
    }
];