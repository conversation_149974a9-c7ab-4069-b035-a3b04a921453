'use client'

import {createContext, useEffect, useState, ReactNode} from 'react'
import {ApplicationType} from '@/lib/types'
import {getApplicationByHostname} from "@/features/application/application.action";
import toast from "react-hot-toast";

type ApplicationContextType = {
    application: Partial<ApplicationType> | null
    loading: boolean
}

export const ApplicationContext = createContext<ApplicationContextType>({
    application: null,
    loading: true,
})


export function ApplicationProvider({
                                        initialApplication,
                                        children,
                                    }: {
    initialApplication?: Partial<ApplicationType> | null
    children: ReactNode
}) {
    const [application, setApplication] = useState<Partial<ApplicationType> | null>(initialApplication || null)
    const [loading, setLoading] = useState(!initialApplication)

    useEffect(() => {
        const fetchApplication = async () => {
            if (!application) {
                // const hostname = window.location.hostname
                const actionResult = await getApplicationByHostname()
                if (!actionResult.success) {
                    toast.error(actionResult.message!)
                    return;
                }
                setApplication(actionResult.data!)
            }
        }

        fetchApplication()
    }, [])

    return (
        <ApplicationContext.Provider value={{application, loading}}>
            {children}
        </ApplicationContext.Provider>
    )
}