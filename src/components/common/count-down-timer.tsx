'use client'

import {useState, useEffect, ReactNode} from "react";

type Props = {
    remainingTime: number;
    onComplete?: () => void;
    renderer?: (second: number, minute: number) => ReactNode;
    onCompleteComponent?: ReactNode;
};

const CountdownTimer = ({remainingTime, onComplete, renderer, onCompleteComponent}: Props) => {
    const [timeLeft, setTimeLeft] = useState(remainingTime);

    useEffect(() => {
        if (timeLeft <= 0) return;

        const interval = setInterval(() => {
            setTimeLeft((prev) => Math.max(prev - 1, 0));
        }, 1000);

        return () => clearInterval(interval);
    }, []);

    // فراخوانی onComplete
    useEffect(() => {
        if (timeLeft === 0 && onComplete) {
            onComplete();
        }
    }, [timeLeft, onComplete]);

    const minutes = Math.floor(timeLeft / 60);
    const secs = timeLeft % 60;

    if (timeLeft === 0) {
        return <>{onCompleteComponent ?? null}</>;
    }

    if (renderer) return renderer(secs, minutes);

    return (
        <span className="text-sm text-gray-600">
            {String(minutes).padStart(2, "0")}:{String(secs).padStart(2, "0")}
        </span>
    );
};

export default CountdownTimer;