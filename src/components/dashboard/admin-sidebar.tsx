"use client"

import Link from "next/link"
import {usePathname} from "next/navigation"
import {
    Package,
} from "lucide-react"

import {
    <PERSON>bar,
    SidebarContent,
    SidebarFooter,
    SidebarGroup,
    SidebarGroupContent,
    SidebarGroupLabel,
    SidebarHeader,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
} from "@/components/ui/sidebar"
import {DASHBOARD_SERVICE_PATH, DASHBOARD_SETTINGS_PATH, HOME_PATH} from "@/lib/routes";
import CustomButton from "@/components/common/custom-button";

export function AdminSidebar() {
    const pathname = usePathname()

    const isActive = (path: string) => {
        return pathname === path || pathname?.startsWith(path + "/")
    }

    return (
        <Sidebar side="right">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/admin">
                                <div
                                    className="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                                    <Package className="size-4"/>
                                </div>
                                <div className="flex flex-col gap-0.5 leading-none">
                                    <span className="font-semibold">پنل مدیریت</span>
                                    <span className="text-xs text-muted-foreground">مدیریت خدمات</span>
                                </div>
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>
            <SidebarContent>
                <SidebarGroup>
                    <SidebarGroupLabel>اصلی</SidebarGroupLabel>
                    <SidebarGroupContent>
                        <SidebarMenu>
                            {/*<SidebarMenuItem>*/}
                            {/*    <SidebarMenuButton asChild isActive={isActive("/admin/categories")}>*/}
                            {/*        <Link href="/admin/categories">*/}
                            {/*            <FolderTree className="size-4"/>*/}
                            {/*            <span>دسته‌بندی‌ها</span>*/}
                            {/*        </Link>*/}
                            {/*    </SidebarMenuButton>*/}
                            {/*</SidebarMenuItem>*/}
                            <SidebarMenuItem>
                                <SidebarMenuButton asChild isActive={isActive(DASHBOARD_SERVICE_PATH)}>
                                    <Link href={DASHBOARD_SERVICE_PATH}>
                                        <Package className="size-4"/>
                                        <span>خدمات</span>
                                    </Link>
                                </SidebarMenuButton>
                            </SidebarMenuItem>
                            <SidebarMenuItem>
                                <SidebarMenuButton asChild isActive={isActive(DASHBOARD_SETTINGS_PATH)}>
                                    <Link href={DASHBOARD_SETTINGS_PATH}>
                                        <Package className="size-4"/>
                                        <span>تنظیمات</span>
                                    </Link>
                                </SidebarMenuButton>
                            </SidebarMenuItem>
                        </SidebarMenu>
                    </SidebarGroupContent>
                </SidebarGroup>
            </SidebarContent>
            <SidebarFooter>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <CustomButton variants='black' href={HOME_PATH}
                                      className='mb-5 !py-2 text-sm'>بازگشت به سایت</CustomButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarFooter>
        </Sidebar>
    )
}
