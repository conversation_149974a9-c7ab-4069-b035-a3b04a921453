import {<PERSON>, Card<PERSON>ontent, CardFooter, CardHeader} from "@/components/ui/card";
import CustomButton from "@/components/common/custom-button";
import {HOME_PATH} from "@/lib/routes";
import {GhabzinoSayadiInquiryResult} from "@/lib/types";
import {miladiToShamsi} from "@/utils/helpers";

type Props = {
    sayadiResult: GhabzinoSayadiInquiryResult
    descriptipn?: string
    sayadId?: string
    newInquiryurl?: string
}

export default function SayadiCheckInquiry({sayadiResult, descriptipn, sayadId, newInquiryurl}: Props) {

    return (

        <Card className="w-full  !h-fit max-w-lg lg:max-w-2xl bg-white rounded-lg overflow-hidden">
            <CardHeader className="flex flex-row justify-between items-start pb-2 pt-6 px-6">
                <h2 className="text-2xl font-semibold text-green-700">نتیجه استعلام</h2>
            </CardHeader>

            <CardContent className="px-6" dir='rtl'>
                <div className="space-y-4">
                    {sayadiResult?.OwnerName && <div className="flex gap-x-5 items-center">
                        <span className="">نام صادر شده:</span>
                        <span
                            className="text-gray-600 font-semibold">{sayadiResult.OwnerName}</span>
                    </div>}

                    {(sayadiResult?.SayadID || sayadId) &&
                        <div className="flex gap-x-5 items-center">
                            <span className="">شناسه صیادی:</span>
                            <span className="text-gray-600 font-semibold">{sayadiResult?.SayadID || sayadId}</span>
                        </div>}

                    {sayadiResult?.Date && <div className="flex gap-x-5 items-center">
                        <span className="">تاریخ چک:</span>
                        <span className="text-gray-600 font-semibold">{miladiToShamsi(sayadiResult.Date)}</span>
                    </div>}

                    {sayadiResult?.Color && <div className="flex gap-x-5 items-center">
                        <span className="">رنگ چک:</span>
                        <span className="text-gray-600 font-semibold">{sayadiResult.Color}</span>
                    </div>}
                    <div className="flex gap-x-5 flex-col justify-start font-semibold gap-y-1 items-start">
                        {/*<span>توضیحات:</span>/*/}
                        <span>{sayadiResult?.Description || descriptipn}</span>
                    </div>
                </div>

            </CardContent>
            <div className="border-t border-gray-200 my-6"></div>
            <CardFooter className="px-6 pb-6">
                <CustomButton href={newInquiryurl || HOME_PATH} variants='green'>استعلام جدید</CustomButton>
            </CardFooter>
        </Card>
    )
}
