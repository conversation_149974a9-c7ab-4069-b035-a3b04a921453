// "use client"
//
// import {useState, useRef, type ChangeEvent} from "react"
// import {PlusCircle, Search, Trash2, Upload, X} from "lucide-react"
// import {Button} from "@/components/ui/button"
// import {Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle} from "@/components/ui/card"
// import {Input} from "@/components/ui/input"
// import {Label} from "@/components/ui/label"
// import {Switch} from "@/components/ui/switch"
// import {Tabs, TabsContent, TabsList, TabsTrigger} from "@/components/ui/tabs"
// import {Badge} from "@/components/ui/badge"
// import Image from "next/image"
// import {toggleCategoryActive, upsertCategory} from "@/actions/category.action";
// import toast from "react-hot-toast";
// import {cn} from "@/lib/utils";
// import {Category} from "@/lib/types";
// import {useCategory} from "@/lib/hooks/useCategory";
// import {
//     AlertDialog, AlertDialogAction, AlertDialogCancel,
//     AlertDialogContent, AlertDialogDescription, AlertDialogFooter,
//     AlertDialogHeader,
//     AlertDialogTitle,
//     AlertDialogTrigger
// } from "@/components/ui/alert-dialog";

// تعریف رابط دسته‌بندی با اضافه کردن فیلد تصویر


export default function CategoriesManagement() {
//     // داده‌های اولیه دسته‌بندی‌ها با تصاویر نمونه
//     const {categories, refetchCategories} = useCategory()
//     const [imagePreview, setImagePreview] = useState<string | undefined>()
//     const [isPending, setIsPending] = useState(false)
//
//     // وضعیت برای دسته‌بندی جدید با اضافه کردن فیلد تصویر
//     const [newCategory, setNewCategory] = useState<Omit<Category, "id" | "servicesCount">>({
//         title: "",
//         active: true,
//         image: undefined,
//         slug: ""
//     })
//
//     const [searchQuery, setSearchQuery] = useState("")
//     const fileInputRef = useRef<HTMLInputElement>(null)
//
//
//     // بروزرسانی عنوان دسته‌بندی
//     const handleUpdateTitle = (id: string, title: string) => {
//         // setCategories(categories.map((category) => (category.id === id ? {...category, title} : category)))
//     }
//
//     // بروزرسانی تصویر دسته‌بندی
//     const handleUpdateImage = (id: string, imageUrl: string | undefined) => {
//         // setCategories(categories.map((category) => (category.id === id ? {...category, imageUrl} : category)))
//     }
//
//     // افزودن دسته‌بندی جدید
//     const handleAddCategory = async () => {
//         setIsPending(true)
//         const formData = new FormData()
//         formData.append('title', newCategory.title)
//         formData.append('active', String(newCategory.active))
//         formData.append('slug', newCategory.slug)
//         if (newCategory?.image) {
//             formData.append('image', newCategory.image)
//         }
//         const actionResult = await upsertCategory(formData)
//
//         if (!actionResult.success) {
//             toast.error(actionResult.message!)
//         } else {
//             toast.success('با موفقیت افزوده شد')
//         }
//         setIsPending(false)
//     }
//
//     const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
//         const file = e.target.files?.[0]
//         if (file) {
//             setNewCategory({...newCategory, image: file})
//
//             const reader = new FileReader()
//             reader.onloadend = () => {
//                 setImagePreview(reader.result as string)
//             }
//             reader.readAsDataURL(file)
//         }
//     }
//
//     // حذف تصویر انتخاب شده برای دسته‌بندی جدید
//     const handleRemoveImage = () => {
//         setNewCategory({...newCategory, image: undefined})
//         if (fileInputRef.current) {
//             fileInputRef.current.value = ""
//         }
//     }
//
//     // فیلتر کردن دسته‌بندی‌ها بر اساس جستجو
//     const filteredCategories = categories.filter((category) =>
//         category.title.toLowerCase().includes(searchQuery.toLowerCase()),
//     )
//
//     const activeCategories = categories.filter((category) => category.active).length
//     // const totalServices = categories.reduce((sum, category) => sum + category.servicesCount, 0)
//
//     return (
//         <div className="h-full flex-1 flex-col space-y-8 p-8 md:flex">
//             <div className="flex items-center justify-between space-y-2">
//                 <div>
//                     <h2 className="text-2xl font-bold tracking-tight">مدیریت دسته‌بندی‌ها</h2>
//                     <p className="text-muted-foreground mt-2">دسته‌بندی‌های خدمات خود را مدیریت کنید.</p>
//                 </div>
//
//             </div>
//             <Tabs defaultValue="all" className="space-y-4">
//                 <div className="flex justify-between flex-col md:flex-row gap-4">
//                     <TabsList>
//                         <TabsTrigger value="all" className='cursor-pointer'>همه دسته‌بندی‌ها</TabsTrigger>
//                         <TabsTrigger value="active" className='cursor-pointer'>فعال</TabsTrigger>
//                         <TabsTrigger value="inactive" className='cursor-pointer'>غیرفعال</TabsTrigger>
//                         <TabsTrigger value="add" className='cursor-pointer'>افزودن جدید</TabsTrigger>
//                     </TabsList>
//
//                     <div className="relative">
//                         <Search className="absolute right-2.5 top-2.5 h-4 w-4 text-muted-foreground"/>
//                         <Input
//                             type="search"
//                             placeholder="جستجوی دسته‌بندی‌ها..."
//                             className="w-full pr-8 md:w-[300px]"
//                             value={searchQuery}
//                             onChange={(e) => setSearchQuery(e.target.value)}
//                         />
//                     </div>
//                 </div>
//                 <TabsContent value="all" className="space-y-4">
//                     <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
//                         <Card>
//                             <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
//                                 <CardTitle className="text-sm font-medium">کل دسته‌بندی‌ها</CardTitle>
//                                 <div className="h-4 w-4 text-muted-foreground"/>
//                             </CardHeader>
//                             <CardContent>
//                                 <div className="text-2xl font-bold">{categories.length}</div>
//                                 <p className="text-xs text-muted-foreground">{activeCategories} دسته‌بندی فعال</p>
//                             </CardContent>
//                         </Card>
//                         <Card>
//                             <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
//                                 <CardTitle className="text-sm font-medium">دسته‌بندی‌های فعال</CardTitle>
//                                 <div className="h-4 w-4 text-muted-foreground"/>
//                             </CardHeader>
//                             <CardContent>
//                                 <div className="text-2xl font-bold">{activeCategories}</div>
//                                 <p className="text-xs text-muted-foreground">
//                                     {((activeCategories / categories.length) * 100).toFixed(0)}٪ از کل دسته‌بندی‌ها
//                                 </p>
//                             </CardContent>
//                         </Card>
//                         <Card>
//                             <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
//                                 <CardTitle className="text-sm font-medium">کل خدمات</CardTitle>
//                                 <div className="h-4 w-4 text-muted-foreground"/>
//                             </CardHeader>
//                             <CardContent>
//                                 <div className="text-2xl font-bold">1</div>
//                                 <p className="text-xs text-muted-foreground">در {activeCategories} دسته‌بندی فعال</p>
//                             </CardContent>
//                         </Card>
//                     </div>
//                     <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
//                         {filteredCategories.map((category) => (
//                             <CategoryCard
//                                 key={category.id}
//                                 category={category}
//                                 onUpdateTitle={handleUpdateTitle}
//                                 onUpdateImage={handleUpdateImage}
//                             />
//                         ))}
//                     </div>
//                 </TabsContent>
//                 <TabsContent value="active" className="space-y-4">
//                     <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
//                         {filteredCategories
//                             .filter((category) => category.active)
//                             .map((category) => (
//                                 <CategoryCard
//                                     key={category.id}
//                                     category={category}
//                                     onUpdateTitle={handleUpdateTitle}
//                                     onUpdateImage={handleUpdateImage}
//                                 />
//                             ))}
//                     </div>
//                 </TabsContent>
//                 <TabsContent value="inactive" className="space-y-4">
//                     <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
//                         {filteredCategories
//                             .filter((category) => !category.active)
//                             .map((category) => (
//                                 <CategoryCard
//                                     key={category.id}
//                                     category={category}
//                                     onUpdateTitle={handleUpdateTitle}
//                                     onUpdateImage={handleUpdateImage}
//                                 />
//                             ))}
//                     </div>
//                 </TabsContent>
//                 <TabsContent value="add" className="space-y-4">
//                     <Card>
//                         <CardHeader>
//                             <CardTitle>افزودن دسته‌بندی جدید</CardTitle>
//                             <CardDescription>دسته‌بندی جدیدی برای خدمات خود ایجاد کنید</CardDescription>
//                         </CardHeader>
//                         <CardContent className="space-y-4">
//                             <div className="space-y-2">
//                                 <Label htmlFor="category-title">عنوان دسته‌بندی</Label>
//                                 <Input
//                                     id="category-title"
//                                     placeholder="مثال: خدمات بانکی"
//                                     value={newCategory.title}
//                                     onChange={(e) => setNewCategory({...newCategory, title: e.target.value})}
//                                 />
//                             </div>
//                             <div className="space-y-2">
//                                 <div className="flex justify-between items-center">
//                                     <Label htmlFor="category-slug">اسلاگ (نامک)</Label>
//                                 </div>
//                                 <div className="flex">
//                                     <Input
//                                         id="category-slug"
//
//                                         placeholder="example-slug"
//                                         value={newCategory.slug}
//                                         onChange={(e) =>
//                                             setNewCategory({
//                                                 ...newCategory,
//                                                 slug: e.target.value.replace(/\s+/g, "-").toLowerCase()
//                                             })
//                                         }
//                                         dir="ltr"
//                                         className="left-direction"
//                                     />
//                                 </div>
//                             </div>
//
//                             {/* بخش آپلود تصویر */}
//                             <div className="space-y-2">
//                                 <Label htmlFor="category-image">تصویر دسته‌بندی</Label>
//                                 <div className="flex flex-col gap-4">
//                                     {newCategory.image ? (
//                                         <div className="relative w-full h-40 bg-muted rounded-md overflow-hidden">
//                                             <Image
//                                                 src={imagePreview || "/placeholder.svg"}
//                                                 alt="تصویر پیش‌نمایش"
//                                                 fill
//                                                 className="object-contain"
//                                             />
//                                             <Button
//                                                 variant="destructive"
//                                                 size="icon"
//                                                 className="absolute top-2 left-2 h-8 w-8"
//                                                 onClick={handleRemoveImage}
//                                             >
//                                                 <X className="h-4 w-4"/>
//                                             </Button>
//                                         </div>
//                                     ) : (
//                                         <div
//                                             className="flex items-center justify-center w-full h-40 bg-muted rounded-md border-2 border-dashed border-muted-foreground/25">
//                                             <div className="flex flex-col items-center gap-2">
//                                                 <Upload className="h-8 w-8 text-muted-foreground"/>
//                                                 <p className="text-sm text-muted-foreground">تصویری را انتخاب کنید یا
//                                                     بکشید و رها کنید</p>
//                                                 <Input
//                                                     ref={fileInputRef}
//                                                     id="category-image"
//                                                     type="file"
//                                                     accept="image/*"
//                                                     className="hidden"
//                                                     onChange={handleFileChange}
//                                                 />
//                                                 <Button
//                                                     variant="secondary"
//                                                     size="sm"
//                                                     className="cursor-pointer"
//                                                     onClick={() => fileInputRef.current?.click()}>
//                                                     انتخاب تصویر
//                                                 </Button>
//                                             </div>
//                                         </div>
//                                     )}
//                                 </div>
//                             </div>
//
//                             <div className="flex items-center space-x-2">
//                                 <Switch
//                                     id="category-enabled"
//                                     checked={newCategory.active}
//                                     onCheckedChange={(checked) => setNewCategory({...newCategory, active: checked})}
//                                     className={cn('left-direction w-10 h-5 cursor-pointer data-[state=checked]:bg-blue-600')}
//                                 />
//                                 <Label htmlFor="category-enabled" className="mr-2">
//                                     فعال‌سازی دسته‌بندی به صورت فوری
//                                 </Label>
//                             </div>
//                         </CardContent>
//                         <CardFooter>
//                             <Button
//                                 disabled={isPending}
//                                 onClick={handleAddCategory}>
//                                 <PlusCircle className="ml-2 h-4 w-4"/>
//                                 افزودن دسته‌بندی
//                             </Button>
//                         </CardFooter>
//                     </Card>
//                 </TabsContent>
//             </Tabs>
//         </div>
//     )
// }
//
// // بروزرسانی رابط پراپس‌های کارت دسته‌بندی
// interface CategoryCardProps {
//     category: Category
//     onUpdateTitle: (id: string, title: string) => void
//     onUpdateImage: (id: string, imageUrl: string | undefined) => void
// }
//
// // بروزرسانی کامپوننت کارت دسته‌بندی با پشتیبانی از تصویر
// function CategoryCard({category, onUpdateTitle, onUpdateImage}: CategoryCardProps) {
//     console.log('category', category)
//     const [isEditing, setIsEditing] = useState(false)
//     const [editedTitle, setEditedTitle] = useState(category.title)
//     const [editedSlug, setEditedSlug] = useState(category.slug)
//     const [editedImageUrl, setEditedImageUrl] = useState<File | string | undefined>(category.image as string)
//     const [imagePreview, setImagePreview] = useState<string | undefined>((editedImageUrl as string) || '')
//     const fileInputRef = useRef<HTMLInputElement>(null)
//     const [isPending, setIsPending] = useState(false)
//     const {refetchCategories} = useCategory()
//
//     const handleSave = async (id: string) => {
//         if (!editedTitle || !editedSlug) {
//             toast.error("عنوان یا اسلاگ نمی تواند خالی باشد");
//             return;
//         }
//         setIsPending(true)
//         const formData = new FormData()
//         formData.append('id', id)
//         formData.append('title', editedTitle)
//         formData.append('slug', editedSlug)
//         if (editedImageUrl && typeof editedImageUrl === 'object') {
//             formData.append('image', editedImageUrl)
//         }
//         const actionResult = await upsertCategory(formData)
//
//         if (!actionResult.success) {
//             toast.error(actionResult.message!)
//         } else {
//             toast.success('با موفقیت افزوده شد')
//             await refetchCategories()
//             setIsEditing(false)
//         }
//         setIsPending(false)
//     }
//
//     const handleToggleCategory = async (id: string) => {
//         setIsPending(true)
//         const actionResult = await toggleCategoryActive(id);
//         if (!actionResult.success) {
//             toast.success(actionResult.message!)
//         } else {
//             await refetchCategories()
//         }
//         setIsPending(false)
//     }
//
//     const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
//         const file = e.target.files?.[0]
//         if (file) {
//             setEditedImageUrl(file)
//             const reader = new FileReader()
//             reader.onloadend = () => {
//                 setImagePreview(reader.result as string)
//             }
//             reader.readAsDataURL(file)
//         }
//     }
//
//     const handleRemoveImage = () => {
//         setEditedImageUrl(undefined)
//         if (fileInputRef.current) {
//             fileInputRef.current.value = ""
//         }
//     }
//
//     const handleDelete = () => {
//         // onDelete(category.id)
//     }
//
//
//     return (
//         <Card className={!category.active ? "opacity-75" : ""}>
//             <CardHeader className="pb-2">
//                 <div className="flex items-center justify-between">
//                     <div className="flex flex-wrap items-center gap-2">
//                         {category.active ? (
//                             <Badge variant="default" className="bg-green-500 text-white">
//                                 فعال
//                             </Badge>
//                         ) : (
//                             <Badge variant="destructive" className="bg-gray-400">
//                                 غیرفعال
//                             </Badge>
//                         )}
//                     </div>
//                     <div className="flex items-center space-x-2">
//                         <Button
//                             variant="ghost"
//                             size="sm"
//                             onClick={() => setIsEditing(!isEditing)}
//                             className="ml-2 cursor-pointer"
//                         >
//                             {isEditing ? "انصراف" : "ویرایش"}
//                         </Button>
//                         <Switch
//                             disabled={isPending}
//                             className={cn('left-direction w-10 h-5 cursor-pointer data-[state=checked]:bg-blue-600')}
//                             checked={category.active}
//                             onCheckedChange={() => handleToggleCategory(category.id!)}/>
//                     </div>
//                 </div>
//             </CardHeader>
//             <CardContent>
//                 {isEditing ? (
//                     <div className="space-y-4">
//                         <div className="space-y-2">
//                             <Label htmlFor={`title-${category.id}`}>عنوان دسته‌بندی</Label>
//                             <Input id={`title-${category.id}`} value={editedTitle}
//                                    onChange={(e) => setEditedTitle(e.target.value)}/>
//                         </div>
//                         <div className="space-y-2">
//                             <div className="flex justify-between items-center">
//                                 <Label htmlFor={`slug-${category.id}`}>اسلاگ (نامک)</Label>
//                             </div>
//                             <Input
//                                 id={`slug-${category.id}`}
//                                 value={editedSlug}
//                                 onChange={(e) => setEditedSlug(e.target.value.replace(/\s+/g, "-").toLowerCase())}
//                                 dir="ltr"
//                                 className="text-left"
//                             />
//                             <p className="text-xs text-muted-foreground">اسلاگ در URL استفاده می‌شود و باید یکتا
//                                 باشد.</p>
//                         </div>
//
//                         {/* بخش ویرایش تصویر */}
//                         <div className="space-y-2">
//                             <Label htmlFor={`image-${category.id}`}>تصویر دسته‌بندی</Label>
//                             <div className="flex flex-col gap-4">
//                                 {editedImageUrl ? (
//                                     <div className="relative w-full h-40 bg-muted rounded-md overflow-hidden">
//                                         <Image
//                                             src={imagePreview || "/placeholder.svg"}
//                                             alt="تصویر پیش‌نمایش"
//                                             fill
//                                             className="object-contain"
//                                         />
//                                         <Button
//                                             variant="destructive"
//                                             size="icon"
//                                             className="absolute top-2 left-2 h-8 w-8"
//                                             onClick={handleRemoveImage}
//                                         >
//                                             <X className="h-4 w-4"/>
//                                         </Button>
//                                     </div>
//                                 ) : (
//                                     <div
//                                         className="flex items-center justify-center w-full h-40 bg-muted rounded-md border-2 border-dashed border-muted-foreground/25">
//                                         <div className="flex flex-col items-center gap-2">
//                                             <Upload className="h-8 w-8 text-muted-foreground"/>
//                                             <p className="text-sm text-muted-foreground">تصویری را انتخاب کنید یا بکشید
//                                                 و رها کنید</p>
//                                             <Input
//                                                 ref={fileInputRef}
//                                                 id={`image-${category.id}`}
//                                                 type="file"
//                                                 accept="image/*"
//                                                 className="hidden"
//                                                 onChange={handleFileChange}
//                                             />
//                                             <Button variant="secondary" size="sm"
//                                                     onClick={() => fileInputRef.current?.click()}>
//                                                 انتخاب تصویر
//                                             </Button>
//                                         </div>
//                                     </div>
//                                 )}
//                             </div>
//                         </div>
//                         <div className="pt-2">
//                             <AlertDialog>
//                                 <AlertDialogTrigger asChild>
//                                     <Button variant="destructive" className="w-full">
//                                         <Trash2 className="ml-2 h-4 w-4"/>
//                                         حذف دسته‌بندی
//                                     </Button>
//                                 </AlertDialogTrigger>
//                                 <AlertDialogContent>
//                                     <AlertDialogHeader>
//                                         <AlertDialogTitle>آیا از حذف این دسته‌بندی اطمینان دارید؟</AlertDialogTitle>
//                                         {/*                <AlertDialogDescription>*/}
//                                         {/*                    این عمل قابل بازگشت نیست. این دسته‌بندی به طور دائمی از سیستم حذف خواهد شد*/}
//                                         {/*                    {category.servicesCount > 0 && (*/}
//                                         {/*                        <span className="block mt-2 text-red-500 font-bold">*/}
//                                         {/*  هشدار: این دسته‌بندی دارای {category.servicesCount} خدمت است که ممکن است تحت تأثیر قرار گیرند.*/}
//                                         {/*</span>*/}
//                                         {/*                    )}*/}
//                                         {/*                </AlertDialogDescription>*/}
//                                     </AlertDialogHeader>
//                                     <AlertDialogFooter>
//                                         <AlertDialogCancel>انصراف</AlertDialogCancel>
//                                         <AlertDialogAction onClick={handleDelete}
//                                                            className="bg-destructive text-destructive-foreground">
//                                             حذف
//                                         </AlertDialogAction>
//                                     </AlertDialogFooter>
//                                 </AlertDialogContent>
//                             </AlertDialog>
//                         </div>
//                     </div>
//                 ) : (
//                     <div className="space-y-4">
//                         <h3 className="font-semibold text-xl">{category.title}</h3>
//                         <div className="text-sm text-muted-foreground">{category.servicesCount} خدمت در این دسته‌بندی
//                         </div>
//
//                         {/* نمایش تصویر دسته‌بندی */}
//                         {category.image && (
//                             <div className="relative w-full h-40 bg-muted rounded-md overflow-hidden">
//                                 <Image
//                                     src={(category.image as string) || "/placeholder.svg"}
//                                     alt={category.title}
//                                     fill
//                                     className="object-contain"
//                                 />
//                             </div>
//                         )}
//                     </div>
//                 )}
//             </CardContent>
//             {isEditing && (
//                 <CardFooter>
//                     <Button disabled={isPending} onClick={() => handleSave(category.id!)}>ذخیره تغییرات</Button>
//                 </CardFooter>
//             )}
//         </Card>
//     )
}
