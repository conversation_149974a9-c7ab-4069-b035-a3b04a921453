# Use Node.js LTS version as the base image
FROM node:23-alpine

# Set the working directory in the container
WORKDIR /app

# Copy package.json and package-lock.json to the container
COPY package*.json ./

# Install dependencies
RUN npm install --force

# Copy the rest of the application code
COPY ./ ./

# Build the Nuxt.js app
RUN npm run build

# Expose the port on which your Nuxt app will run
EXPOSE 3000

# Command to run the application
CMD ["npm", "start"]
