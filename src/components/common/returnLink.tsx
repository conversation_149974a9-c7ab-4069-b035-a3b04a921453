'use client'

import {ArrowRight} from "lucide-react";
import {useRouter} from "next/navigation";

export default function ReturnLink() {
    const router = useRouter();

    function handleReturnUrl() {
        router.back()
    }

    return (
        <div onClick={handleReturnUrl}
             className="flex items-center text-gray-500 hover:text-green-700 font-bold cursor-pointer">
            <ArrowRight className="h-4 w-4 ml-1"/>
            <span className="text-right">بازگشت</span>
        </div>
    );
}
