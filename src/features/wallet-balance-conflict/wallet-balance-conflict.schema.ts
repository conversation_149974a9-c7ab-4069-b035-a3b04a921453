import {Schema, model, models, Model} from 'mongoose';
import {SOURCE_NAME} from "@/lib/constants";
import {IWalletConflict} from "@/lib/types";


const WalletBalanceConflictUserSchema = new Schema<IWalletConflict>(
    {
        phone: {
            type: String,
        },
        user: {
            type: Schema.Types.ObjectId,
            ref: 'User',
        },
        source: {
            type: String,
            enum: [SOURCE_NAME],
            default: SOURCE_NAME,
        },
        balance_in_wallet: {
            type: Number,
        },
        description: {
            type: String,
            required: true
        },
        balance_in_user: {
            type: Number,
        },
    },
    {
        timestamps: true,
        collection: 'wallet_balance_conflict',
    }
);

const WalletConflictModel: Model<IWalletConflict> =
    models.WalletConflict || model<IWalletConflict>('WalletConflict', WalletBalanceConflictUserSchema);
export default WalletConflictModel;