import WalletModel from "@/features/wallet/wallet.schema";
import {IWallet, WalletCreateInputType} from "@/lib/types";
import {ClientSession} from "mongoose";

class WalletRepository {

    async createEntry({
                          userId,
                          amount,
                          phone,
                          referenceType,
                          reason,
                          referenceId,
                          source,
                          balanceAfter,
                          refundId,
                      }: WalletCreateInputType,
                      session: ClientSession | null = null): Promise<IWallet | undefined> {

        const balanceBefore = balanceAfter - amount;

        const newWallet = new WalletModel({
            user: userId,
            phone,
            amount,
            refundId,
            source,
            balance_before: balanceBefore,
            balance_after: balanceAfter,
            reference_type: referenceType,
            reference_id: referenceId,
            reason,
        });

        return newWallet.save({session});

    }
}

export default new WalletRepository();