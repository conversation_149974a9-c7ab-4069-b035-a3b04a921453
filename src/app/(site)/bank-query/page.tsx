import Container from "@/components/common/container";
import InquiryWrapperComponent from "@/components/exchange/inquiryWrapperComponent";
import {CategorySlugEnum} from "@/lib/types";

// export const dynamic = 'force-dynamic';
export default async function BankQueryPage() {
    return (
        <>
            <Container>
                <div className="w-full">
                    <InquiryWrapperComponent
                        categorySlug={CategorySlugEnum.BANKING_INQUIRY}
                        description='استعلام شماره کارت و شبا'
                        categoryTitle='پشتیبانی از تمام بانک ها'
                    />
                </div>
            </Container>
        </>
    );
}
