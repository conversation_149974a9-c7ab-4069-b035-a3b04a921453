'use client'

import React, {ReactNode, useImperativeHandle, useRef} from 'react';
import {cn} from "@/lib/utils";
import {MOTOR_PLATE_LEFT, MOTOR_PLATE_RIGHT, motorPlatePartsMaxLengths} from '@/lib/constants';
import PlateCarLeftIcon from "@/components/common/svg/PlateCarLeftIcon";
import {PlateInputRef} from "@/components/common/car-plate-input";

type Props = {
    value?: string[],
    onChange?: (value: string[]) => void,
    readOnly?: boolean,
    ref?: React.Ref<PlateInputRef | null>,
}

type ReadOnlyFieldProps = {
    value: string | ReactNode
}


function ReadOnlyField({value}: ReadOnlyFieldProps) {
    return (
        <div
            className='flex justify-center items-center left-direction !text-2xl text-center px-0 h-full py-0'
        >
            <span>
              {value}
           </span>
        </div>
    )
}


export default function PlateInputMotor({ref, onChange, readOnly, value = ["", ""]}: Props) {
    const inputRefs = [useRef<HTMLInputElement | null>(null), useRef<HTMLInputElement | null>(null)];

    const handleInputChange = (partIndex: number, newValue: string) => {
        const isNotValid = (partIndex === MOTOR_PLATE_LEFT && newValue.length > motorPlatePartsMaxLengths[MOTOR_PLATE_LEFT]) ||
            (partIndex === MOTOR_PLATE_RIGHT && newValue.length > motorPlatePartsMaxLengths[MOTOR_PLATE_RIGHT])
        if (isNotValid) {
            return;
        }
        const updatedParts = [...value];
        updatedParts[partIndex] = newValue;
        onChange?.(updatedParts);
    };

    const handleKeyUp = (e: React.KeyboardEvent<HTMLInputElement>, currentIndex: number) => {
        if (e.key === "Backspace" && e.currentTarget.value.length === 0 && currentIndex > MOTOR_PLATE_LEFT) {
            const prevIndex = currentIndex - 1;
            inputRefs[prevIndex].current?.focus();
        } else if (e.key !== "Backspace" && e.currentTarget.value.length >= motorPlatePartsMaxLengths[currentIndex] && currentIndex < MOTOR_PLATE_RIGHT) {
            const nextIndex = currentIndex + 1;
            inputRefs[nextIndex].current?.focus();
        }
    };

    const handleInput = (event: React.ChangeEvent<HTMLInputElement>) => {
        event.target.value = event.target.value.replace(/\D/g, "");
    };

    useImperativeHandle(ref, () => ({
        focus: () => {
            if (inputRefs[0].current) {
                inputRefs[0].current.focus(); // Focus the first input
            }
        },
    }));

    return (
        <div
            className={cn('w-full overflow-hidden rounded-lg border-2 border-l-0 border-[#000000] h-[60px] flex items-center justify-between gap-1', {
                "h-[55px]": readOnly,
                "h-[63px]": !readOnly,
            })}
            style={{direction: 'ltr'}}>
            <div
                className='w-[10%] flex justify-center bg-[#1D389A] border border-[#1D389A]  min-w-[35px] shrink-0 rounded-tl-xl rounded-bl-xl'>
                <PlateCarLeftIcon width={35} height={30}/>
            </div>
            <div className='w-[30%] h-full flex flex-col justify-center items-center'>
                {

                    readOnly ? (<ReadOnlyField value={value[MOTOR_PLATE_LEFT]}/>) : (
                        <input
                            ref={(el) => {
                                inputRefs[MOTOR_PLATE_LEFT].current = el;
                            }}
                            onInput={handleInput}
                            autoFocus
                            value={value[MOTOR_PLATE_LEFT]}
                            onChange={(e) => handleInputChange(MOTOR_PLATE_LEFT, e.target.value)}
                            onKeyUp={(e) => handleKeyUp(e, MOTOR_PLATE_LEFT)}
                            maxLength={motorPlatePartsMaxLengths[MOTOR_PLATE_LEFT]}
                            type='tel'
                            className="w-full left-direction borderless-input !text-xl text-center px-0 h-full py-0"
                            inputMode="numeric"
                            placeholder=" - - - "
                        />
                    )
                }
            </div>
            <div className="bg-neutral-300 h-[60%] w-[1px] my-2 "></div>
            <div className='flex flex-col w-[40%] items-center'>
                {
                    readOnly ? (<ReadOnlyField value={value[MOTOR_PLATE_RIGHT]}/>) : (
                        <input
                            ref={(el) => {
                                inputRefs[MOTOR_PLATE_RIGHT].current = el;
                            }}
                            onInput={handleInput}
                            value={value[MOTOR_PLATE_RIGHT]}
                            onChange={(e) => handleInputChange(MOTOR_PLATE_RIGHT, e.target.value)}
                            onKeyUp={(e) => handleKeyUp(e, MOTOR_PLATE_RIGHT)}
                            maxLength={motorPlatePartsMaxLengths[MOTOR_PLATE_RIGHT]}
                            type='tel'
                            className="w-full left-direction borderless-input !text-xl text-center px-0 h-full py-0 "
                            inputMode="numeric"
                            placeholder=" - - - - - "
                        />
                    )
                }
            </div>
        </div>
    );
}
