'use client'

import ReturnLink from "@/components/common/returnLink";
import {useApplication} from "@/lib/hooks/useApplication";

export default function RuleComponent() {
    const {application} = useApplication()
    const appTitle = application?.title ?? ""
    return (
        <div className="min-h-screen bg-gray-50 flex justify-center py-8 px-4">
            <div className="w-full max-w-4xl bg-white rounded-lg shadow-sm p-6 md:p-8">
                <div className="flex justify-between items-center mb-6">
                    <ReturnLink/>
                </div>

                <div className="space-y-8 text-right" dir="rtl">
                    <h1 className="text-2xl font-bold text-center mb-8">قوانین و مقررات</h1>

                    <section>
                        <h2 className="text-lg font-bold mb-2">تطابق با قوانین جمهوری اسلامی ایران</h2>
                        <p className="text-gray-800 leading-relaxed">
                            تمامی اصول و قواعد سایت {appTitle} با قوانین جمهوری اسلامی ایران، قوانین رعایت حقوق
                            مصرف کننده و قانون
                            تجارت الکترونیک مطابقت دارد.
                        </p>
                    </section>

                    <section>
                        <h2 className="text-lg font-bold mb-2">تغییر در شروط توافقنامه</h2>
                        <p className="text-gray-800 leading-relaxed">
                            {appTitle} می تواند شروط این توافقنامه را تغییر داده و موظف است آن را از طریق سایت به
                            کاربران خود اعلام
                            کند.
                        </p>
                    </section>

                    <section>
                        <h2 className="text-lg font-bold mb-2">مدت زمان اعتبار شارژ</h2>
                        <p className="text-gray-800 leading-relaxed">
                            شارژ انجام شده در پنل کاربری شما تا ۱۸ ماه قابل استفاده است. بعد از این مدت زمان، هر
                            میزان از شارژ که باقی
                            مانده باشد از بین می رود.
                        </p>
                    </section>

                    <section>
                        <h2 className="text-lg font-bold mb-2">برگشت وجه</h2>
                        <p className="text-gray-800 leading-relaxed mb-2">
                            به دلیل سهولت در استفاده از خدمات، هزینهٔ تمامی خدمات در {appTitle} برحسب «تومان» ارائه
                            می‌شود.
                        </p>
                        <ol className="list-decimal list-inside space-y-2 pr-4">
                            <li className="text-gray-800">
                                برگشت وجه تنها در صورت عدم عملکرد سایت {appTitle} انجام میگیرد و شامل خطاهای احتمالی
                                از سمت بانک مرکزی
                                نمی باشد.
                            </li>
                            <li className="text-gray-800">
                                در صورتی که کاربر اقدام به پرداخت مبلغ اشتباه کند، مسئولیت آن با خود کاربر است و
                                امکان برگشت وجه وجود
                                ندارد.
                            </li>
                            <li className="text-gray-800">
                                موجودی حساب کاربری ( کیف پول ) به هیچ عنوان امکان برداشت به حساب بانکی را ندارد.
                            </li>
                            <li className="text-gray-800">
                                مبالغ شارژ شده به هیچ عنوان قابل تسویه حساب به حساب بانکی نیست و کاربران میبایستی
                                حداکثر دقت را در شارژ
                                حساب کاربری داشته باشند.
                            </li>
                        </ol>
                    </section>

                    <section>
                        <h2 className="text-lg font-bold mb-2">مسئولیت واریز به حساب بانکی</h2>
                        <p className="text-gray-800 leading-relaxed">
                            تمامی استعلام های بانکی از طریق وب سرویس های واسطه دریافت شده و به متقاضی ارائه می شود.
                            با این حال، کاربر
                            خود موظف است که قبل از واریز مبلغ شارژ به حساب اعلام شده، مجددا نسبت به چک کردن نام حساب
                            بانکی گیرنده
                            اقدام کند. در غیر این صورت، {appTitle} هیچ مسئولیتی را در این زمینه نمی پذیرد.
                        </p>
                    </section>

                    <section>
                        <h2 className="text-lg font-bold mb-2">اعتبار استعلام</h2>
                        <p className="text-gray-800 leading-relaxed">
                            اعتبار استعلام اعلام شده به کاربر تا همان زمان استعلام اعتبار دارد.
                        </p>
                    </section>

                    <section>
                        <h2 className="text-lg font-bold mb-2">مطالعه قوانین و مقررات</h2>
                        <p className="text-gray-800 leading-relaxed">
                            کاربر {appTitle} موظف است برای استفاده از خدمات این سایت، قوانین و مقررات را به صورت
                            هفتگی و مستمر مطالعه
                            کند. در صورت مغایرت توقع کاربر با قوانین و مقررات جدید، تمامی مسئولیت ها بر عهده کاربر
                            خواهد بود.
                        </p>
                    </section>

                    <p className="text-center font-bold mt-10 pb-4">
                        با استفاده از خدمات {appTitle}، شما به طور کامل با قوانین و مقررات این سایت موافقت می کنید.
                    </p>
                </div>
            </div>
        </div>
    );
}
