import {Card, CardContent} from "@/components/ui/card";
import {Search} from "lucide-react";
import ReturnLink from "@/components/common/returnLink";

export default function NoInquiryHistory() {
    return (
        <Card className="w-full max-w-xl h-fit overflow-hidden">
            <CardContent className="py-4 px-8 text-center">
                <ReturnLink/>
                <div className="flex flex-col items-center justify-center gap-6">
                    <div className="rounded-full bg-slate-100 p-3">
                        <Search className="h-8 w-8 text-slate-400"/>
                    </div>
                    <h3 className="text-xl font-medium">استعلامی یافت نشد</h3>
                </div>
            </CardContent>
        </Card>
    );
}
