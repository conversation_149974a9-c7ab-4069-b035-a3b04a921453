import {Card, CardContent, CardHeader} from "@/components/ui/card"
import {
    GhabzinoBankType,
    GhabzinoResultType,
    InquiryTypeEnum,
    JibitResultType,
    VehiclePaymentDetails
} from "@/lib/types";
import JibitBankInquiryHistoryContent from "@/components/history/jibit-bank-inquiry-history-content";
import GhabzinoSayadCheckHistoryContent from "@/components/history/ghabzino-sayad-check-history-content";
import GhabzinoTrafficFineContent from "@/components/history/ghabzino-traffic-fine-history";
import {Fragment} from "react";
import GhabzinoBankInquiryHistoryContent from "@/components/history/ghabzino-bank-inquiry-history-content";

type Props = {
    inquiries: (JibitResultType | GhabzinoResultType | Partial<VehiclePaymentDetails> | undefined)[];
}

export default function InquiryHistory({inquiries}: Props) {


    return (
        <div className="mx-auto w-full max-w-3xl">
            <h1 className="text-center text-xl md:text-2xl font-medium text-emerald-600 mb-6 md:mb-14">استعلام های
                اخیر</h1>
            <Card className="overflow-hidden shadow-xl">
                <CardHeader className="bg-white py-4 text-right">
                    <p className='text-neutral-600 text-sm md:text-base'>لیست استعلاماتی که در گذشته انجام
                        داده اید تا قبل از منقضی شدن در این صفحه
                        نمایش
                        داده میشود. </p>
                </CardHeader>
                <CardContent className="">
                    {
                        inquiries.map((inq, index) => {
                            const isJibitBankType = inq!.inquiryType === InquiryTypeEnum.Jibit_DepositToIban ||
                                inq!.inquiryType === InquiryTypeEnum.Jibit_CardToIban ||
                                inq!.inquiryType === InquiryTypeEnum.Jibit_CardToDeposit ||
                                inq!.inquiryType === InquiryTypeEnum.Jibit_IbanInquiry ||
                                inq!.inquiryType === InquiryTypeEnum.Jibit_CardInquiry
                            const isSayadiType = inq!.inquiryType === InquiryTypeEnum.Ghabzino_SayadCheckInquiry
                            const isKhalafiType = inq!.inquiryType === InquiryTypeEnum.Ghanzino_KhalafiKhodro ||
                                inq!.inquiryType === InquiryTypeEnum.Ghanzino_KhalafiMotor
                            const isGhabzinoBankType = inq!.inquiryType === InquiryTypeEnum.Ghanzino_DepositToIban ||
                                inq!.inquiryType === InquiryTypeEnum.Ghanzino_CardToIban

                            return (
                                <Fragment key={index}>
                                    {isJibitBankType &&
                                        <JibitBankInquiryHistoryContent inquiry={inq as JibitResultType}/>}
                                    {isGhabzinoBankType &&
                                        <GhabzinoBankInquiryHistoryContent inquiry={inq as GhabzinoBankType}/>}
                                    {isSayadiType &&
                                        <GhabzinoSayadCheckHistoryContent inquiry={inq as GhabzinoResultType}/>}
                                    {isKhalafiType &&
                                        <GhabzinoTrafficFineContent inquiry={inq as VehiclePaymentDetails}/>}
                                </Fragment>
                            )
                        })
                    }

                </CardContent>

            </Card>
        </div>
    )
}

