import {model, Model, models, Schema} from 'mongoose';
import {ITransaction, PaymentGate, PaymentStatus} from "@/lib/types";

const transactionSchema: Schema = new Schema<ITransaction>({
        gate: {
            type: String,
            enum: Object.values(PaymentGate),
            required: true,
            default: PaymentGate.ZARINPAL
        },
        terminalId: {
            type: String,
            required: true
        },
        phone: {
            type: String,
            required: true
        },
        amount: {
            type: Number,
            required: true
        },
        paymentCode: {
            type: String,
        },
        code: {
            type: String,
        },
        source: {
            type: String,
            required: true,
        },
        authority: {
            type: String,
            required: true
        },
        inquiry: Schema.Types.Mixed,
        cardNumber: {
            type: String,
            default: ''
        },
        cardHashPan: {
            type: String,
        },
        ip: {
            type: String,
            required: true
        },
        agent: {
            type: String,
            required: true
        },
        settlementCode: {
            type: String,
        },
        clientRefId: {
            type: String,
        },
        status: {
            type: String,
            enum: Object.values(PaymentStatus),
            default: PaymentStatus.PENDING,
        },
        refId: {
            type: String,
            default: '',
        },
        result_payment: {
            Status: Number,
            RefId: String
        }
    },
    {
        timestamps: true,
        collection: 'transactions'
    });


const transactionModel: Model<ITransaction> =
    (models.transactions) ||
    model<ITransaction>("transactions", transactionSchema);

export default transactionModel;

