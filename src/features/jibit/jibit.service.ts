import {IInquiryService} from "@/lib/types";
import {BaseJibit} from "@/features/jibit/types/base-jibit";

class JibitService implements BaseJibit {
    private _cardToIban?: IInquiryService<any, any>;
    private _depositToIban?: IInquiryService<any, any>;
    private _cardToDeposit?: IInquiryService<any, any>;
    private _iban?: IInquiryService<any, any>;
    private _cardNumber?: IInquiryService<any, any>;

    async cardToIban(): Promise<IInquiryService<any, any>> {
        if (!this._cardToIban) {
            const cardToIbanModule = await import("@/features/jibit/services/card-to-iban.service");
            this._cardToIban = new cardToIbanModule.default();
        }
        return this._cardToIban;
    }

    async depositToIban(): Promise<IInquiryService<any, any>> {
        if (!this._depositToIban) {
            const depositToIbanModule = await import("@/features/jibit/services/deposit-to-iban.service");
            this._depositToIban = new depositToIbanModule.default();
        }
        return this._depositToIban;
    }

    async cardToDeposit(): Promise<IInquiryService<any, any>> {
        if (!this._cardToDeposit) {
            const cardToDepositModule = await import("@/features/jibit/services/card-to-deposit.service");
            this._cardToDeposit = new cardToDepositModule.default();
        }
        return this._cardToDeposit;
    }

    async iban(): Promise<IInquiryService<any, any>> {
        if (!this._iban) {
            const ibanModule = await import("@/features/jibit/services/iban.service");
            this._iban = new ibanModule.default();
        }
        return this._iban;
    }

    async cardNumber(): Promise<IInquiryService<any, any>> {
        if (!this._cardNumber) {
            const cardNumberModule = await import("@/features/jibit/services/card-number.service");
            this._cardNumber = new cardNumberModule.default();
        }
        return this._cardNumber;
    }
}

export default new JibitService();