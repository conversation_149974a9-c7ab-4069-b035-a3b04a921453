'use client'

import {useAuth} from "@/lib/hooks/useAuth";
import {formatWithComma} from "@/utils/helpers";

export default function DepoUserInfo() {
    const {userData} = useAuth()

    if (!userData) return null;
    return (
        <div
            className="w-full md:min-w-xl h-fit rounded-xl mb-5 bg-neutral-100 px-6 md:px-14 py-5 shadow-lg flex flex-col justify-center items-center gap-y-2">
            <div className='w-[200px] flex justify-between items-center '>
                <span className='text-neutral-700'>شماره شما:</span>
                <span>{userData.phone}</span>
            </div>
            <div className='w-[200px] text-right flex justify-between items-center'>
                <span className='text-neutral-700'>اعتبار شما:</span>
                <div className='flex items-center gap-x-1'>
                    <span>تومان</span>
                    <span>{formatWithComma(userData.balance.toString())}</span>
                </div>
            </div>
        </div>
    );
}
