import {InquiryTypeEnum, ServiceComponentProps} from "@/lib/types";

export const componentMap: Record<InquiryTypeEnum, () => Promise<React.ComponentType<ServiceComponentProps>>> = {
    [InquiryTypeEnum.Jibit_CardToIban]: () => import('./services/jibit/card-to-iban-form').then(mod => mod.default),
    [InquiryTypeEnum.Jibit_DepositToIban]: () => import('./services/jibit/deposit-to-iban-form').then(mod => mod.default),
    [InquiryTypeEnum.Jibit_CardToDeposit]: () => import('./services/jibit/card-to-deposit-form').then(mod => mod.default),
    [InquiryTypeEnum.Jibit_IbanInquiry]: () => import('./services/jibit/iban-inquiry').then(mod => mod.default),
    [InquiryTypeEnum.Jibit_CardInquiry]: () => import('./services/jibit/card-inquiry').then(mod => mod.default),
    [InquiryTypeEnum.Ghabzino_SayadCheckInquiry]: () => import('./services/ghabzino/sayadi-check-form').then(mod => mod.default),
    [InquiryTypeEnum.Ghanzino_KhalafiKhodro]: () => import('./services/ghabzino/khalafi-khodro-form').then(mod => mod.default),
    [InquiryTypeEnum.Ghanzino_KhalafiMotor]: () => import('./services/ghabzino/khalafi-motor-form').then(mod => mod.default),
    [InquiryTypeEnum.Ghanzino_DepositToIban]: () => import('./services/ghabzino/deposit-to-iban-form').then(mod => mod.default),
    [InquiryTypeEnum.Ghanzino_CardToIban]: () => import('./services/ghabzino/card-to-iban-form').then(mod => mod.default),

};