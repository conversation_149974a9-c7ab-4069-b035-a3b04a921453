import userTokenModel from "@/features/user-token/user-tokens.schema";
import {CreateTokenInputType, IUserToken} from "@/lib/types";
import {ClientSession} from "mongoose";

class UserTokenRepository {

    async findUserTokenForOtp(
        {phone, source}: { phone: string, source: string }, session: ClientSession | null = null
    ): Promise<IUserToken | null> {
        const currentDate = new Date();
        return userTokenModel.findOne({
            phone,
            source,
            status: 0,
            expiredIn: {$gt: currentDate},
        }).session(session);
    };

    async createUserToken({
                              phone,
                              ip,
                              agent,
                              accessToken,
                              otpToken,
                              expiredIn,
                              source,
                          }: CreateTokenInputType, session: ClientSession | null = null): Promise<IUserToken> {

        const userToken = new userTokenModel({
            access_token: accessToken,
            token: otpToken,
            phone,
            ip,
            agent,
            expiredIn,
            status: 0,
            source,
        });


        return await userToken.save({session});
    };

}

export default new UserTokenRepository();