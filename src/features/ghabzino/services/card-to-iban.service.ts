import {
    BaseParamsType,
    GenericResponse, GhabzinoBankResult,
    GhabzinoResponse,
    IInquiryService, InquiryStatus, InquiryTypeEnum, ProviderType,
    ServiceResult
} from "@/lib/types";
import inquiryRepository from "@/features/inquiry/inquiry.repository";
import {InternalServerError} from "@/lib/error";
import {handleInquiryServiceError} from "@/utils/helpers-server";
import {ClientSession} from "mongoose";
import GhabzinoFetchclient from "@/features/ghabzino/ghabzino.fetchClient";

type CardToIbanInquiryInput = {
    cardNumber: string,
    accountTypeName: string,
} & BaseParamsType

class CardToIbanService implements IInquiryService<CardToIbanInquiryInput, ServiceResult | undefined> {

    async lookup({
                     accountTypeName,
                     cardNumber,
                     validPeriod,
                     userId,
                     phone,
                     source,
                     balance,
                     balance_after,
                 }: CardToIbanInquiryInput, session: ClientSession | null = null): Promise<GenericResponse<ServiceResult | undefined>> {
        try {

            // const depositToIbanFromDb = await inquiryRepository.findOneGhabzinoCardToIban({
            //     accountTypeName,
            //     cardNumber,
            //     requireValidExpireTime: false
            // }, session);

            const createBaseParams: any = {
                accountTypeName,
                cardNumber,
                source,
                user: userId,
                provider: ProviderType.GHABZINO,
                phone,
                balance,
                inquiryType: InquiryTypeEnum.Ghanzino_CardToIban,
            }

            let response: GhabzinoResponse<GhabzinoBankResult> | undefined;
            // let success = !!depositToIbanFromDb
            // if (!depositToIbanFromDb) {
            try {
                response = await GhabzinoFetchclient.cardToIbanInquiry({
                    cardNumber,
                    accountTypeName
                })
            } catch (error: any) {

                await inquiryRepository.create<GhabzinoResponse<GhabzinoBankResult>>({
                    ...createBaseParams,
                    inquiryStatus: InquiryStatus.FAILED,
                    inquiryResult: error?.message ? error.message : error,
                }, session);

                return {
                    success: "FAILED",
                    message: 'استعلام شما ناموفق بود لطفا مجددا تلاش کنید'
                }
            }
            const successCodes = ['G00000', 'GD2250', 'GD2255', 'GD2259'];
            const success = successCodes.includes(response?.Status?.Code);
            // }

            const savedResult = await inquiryRepository.create<GhabzinoResponse<GhabzinoBankResult>>({
                ...createBaseParams,
                inquiryStatus: success ? InquiryStatus.SUCCESS : InquiryStatus.FAILED,
                validPeriod: success ? validPeriod : undefined,
                inquiryResult: response,
                paid: success,
                balance_after: success ? balance_after : undefined,
            }, session);

            if (!savedResult) {
                throw new InternalServerError();
            }

            if (success) {
                return {
                    success: 'SUCCESS',
                    data: {
                        id: savedResult._id.toString(),
                        trackingNumber: savedResult.trackingNumber!,
                    }
                };
            }

            return {
                success: 'FAILED',
                message: response?.Status?.Description || 'خطای ناشناخته‌ای رخ داده است',
            };

        } catch (error: any) {
            return handleInquiryServiceError(error);
        }
    }
}

export default CardToIbanService;