import {IApplication} from "@/lib/types";
import ApplicationModel from "@/features/application/application.schema";
import {applicationSeed} from "@/lib/data/application";
import {SOURCE_NAME} from "@/lib/constants";
import {ClientSession} from "mongoose";

class ApplicationRepository {

    async getByHost(hostName: string, session: ClientSession | null = null): Promise<IApplication | null> {
        return ApplicationModel.findOne({
            source: hostName,
            subset: SOURCE_NAME
        }).session(session)
    }

    async getAll(): Promise<IApplication[]> {
        return ApplicationModel.find({
            subset: SOURCE_NAME
        })
    }

    async findOneAndUpdate({
                               id,
                               title,
                               pattern_login,
                               merchent_id,
                               ga_measurementId,
                               ga_apiSecret,
                               ga_containerId
                           }: Partial<IApplication>): Promise<IApplication | null> {
        console.log('Repository received data:', {
            id,
            title,
            pattern_login,
            merchent_id,
            ga_measurementId,
            ga_apiSecret,
            ga_containerId
        });

        const result = ApplicationModel.findOneAndUpdate({
                _id: id,
                subset: SOURCE_NAME
            },
            {
                title,
                pattern_login,
                merchent_id,
                ga_measurementId,
                ga_apiSecret,
                ga_containerId
            },
            {new: true}
        );

        console.log('Repository update result:', result);
        return result;
    }

    async seed(hostName: string): Promise<IApplication> {
        const newApplication = new ApplicationModel(
            {
                source: hostName,
                status: applicationSeed.status,
                pattern_login: applicationSeed.pattern_login,
                merchent_id: applicationSeed.merchent_id,
                title: applicationSeed.title
            }
        )

        return newApplication.save();
    };

}

export default new ApplicationRepository();