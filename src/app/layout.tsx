import type {Metada<PERSON>} from "next";
import "../styles/globals.css";
import {Toaster} from "react-hot-toast";
import Footer from "@/components/common/footer/footer";
import {UserProvider} from "@/lib/providers/UserProvider";
import NextTopLoader from 'nextjs-toploader';
import {createService, getOrSeedAppServices} from "@/features/service/service.action";
import {ServiceProvider} from "@/lib/providers/service-provider";
import {GoogleTagManager} from '@next/third-parties/google'
import {ApplicationProvider} from "@/lib/providers/application-provider";
import {getApplicationByHostname} from "@/features/application/application.action";
import GclidHandler from "@/components/common/GclidHandler";
import {
    ApplicationType,
    CategorySlugEnum,
    InquiryPolicyEnum,
    InquiryTypeEnum,
    ProviderType,
    SettingsValueTypeEnum
} from "@/lib/types";


export const metadata: Metadata = {
    description: "سایت استعلام آنلاین",
    robots: {
        index: false,
        follow: false,
    },
    icons: {
        icon: '/favicon.ico',
    }
};
export const dynamic = 'force-dynamic';
export default async function RootLayout({
                                             children,
                                         }: Readonly<{
    children: React.ReactNode;
}>) {

    const [serviceActionResult, applicationActionResult] = await Promise.all([getOrSeedAppServices(), getApplicationByHostname()]);

    if (!applicationActionResult.success) {
        console.error(applicationActionResult.message!);

    }

    if (!serviceActionResult.success) {
        console.error(serviceActionResult.message!);
    }

    const containerId = applicationActionResult?.data?.ga_containerId
    const initialApplication: Partial<ApplicationType> | null = applicationActionResult?.data ? {
        source: applicationActionResult.data.source,
        title: applicationActionResult.data.title,
        pattern_login: applicationActionResult.data.pattern_login
    } : null;

    return (
        <html>
        {containerId && <GoogleTagManager gtmId={containerId}/>}
        <body>
        <NextTopLoader/>
        <div className="min-h-screen bg-slate-50">
            <ApplicationProvider initialApplication={initialApplication}>
                <UserProvider>
                    <GclidHandler/>
                    {/*<CategoryProvider initialCategories={categoryActionResult.data || []}>*/}
                    <ServiceProvider initialServices={(serviceActionResult.data as any) || []}>
                        {children}
                    </ServiceProvider>
                    {/*</CategoryProvider>*/}
                    <Footer/>
                </UserProvider>
            </ApplicationProvider>
        </div>
        <Toaster/>
        </body>
        </html>
    );
}
