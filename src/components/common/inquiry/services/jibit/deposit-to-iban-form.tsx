'use client'

import {Form, FormControl, FormField, FormItem, FormMessage} from "@/components/ui/form";
import {InquiryTypeEnum, ServiceComponentProps} from "@/lib/types";
import CustomInput from "@/components/common/custom-input";
import {Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select";
import {banks} from "@/lib/data/jibit-bank-list";
import CustomButton from "@/components/common/custom-button";
import {useForm} from "react-hook-form";
import {jibitAccountToIbanSchema, JibitAccountToIbanType} from "@/lib/zod-schemas";
import {zodResolver} from "@hookform/resolvers/zod";
import InquiryPrice from "@/components/common/inquiry/inquiry-price";

export default function DepositToIbanForm({isLoading, onSubmit, price}: ServiceComponentProps) {

    const formDepositToIban = useForm<JibitAccountToIbanType>({
        resolver: zodResolver(jibitAccountToIbanSchema),
        mode: 'onSubmit',
        defaultValues: {
            depositNumber: "",
            bank: ""
        },
    })

    async function handleSubmit(values: any) {
        if (isLoading) return;
        onSubmit?.(values, InquiryTypeEnum.Jibit_DepositToIban)
    }

    return (
        <>
            <Form {...formDepositToIban}>
                <form
                    onSubmit={formDepositToIban.handleSubmit((values) => handleSubmit(values))}
                    className="space-y-6">
                    <FormField
                        control={formDepositToIban.control}
                        name="depositNumber"
                        render={({field}) => (
                            <FormItem className='w-full'>
                                <label className='text-neutral-600 text-sm'>شماره حساب</label>
                                <FormControl>
                                    <CustomInput
                                        autoFocus
                                        type='text'
                                        maxLength={20}
                                        placeholder='شماره حساب خود را وارد کنید'
                                        {...field}
                                        value={field.value}
                                        onChange={(value) => field.onChange(value)}
                                    />
                                </FormControl>
                                <FormMessage className='text-sm'/>
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={formDepositToIban.control}
                        name="bank"
                        render={({field}) => (
                            <FormItem className='w-full'>
                                <label className='text-neutral-600 text-sm'>نام بانک</label>
                                <FormControl>
                                    <Select
                                        {...field}
                                        value={field.value}
                                        onValueChange={(value) => field.onChange(value)}
                                    >
                                        <SelectTrigger className="w-full cursor-pointer h-[45px] md:!h-[60px]">
                                            <SelectValue className='text-neutral-400'
                                                         placeholder="بانک خود را انتخاب کنید"/>
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectGroup>
                                                {
                                                    banks.map((bank) => (
                                                        <SelectItem
                                                            key={bank.bankId}
                                                            value={bank.bankId}>{bank.bankNameFarsi}</SelectItem>
                                                    ))
                                                }

                                            </SelectGroup>
                                        </SelectContent>
                                    </Select>
                                </FormControl>
                                <FormMessage className='text-sm'/>
                            </FormItem>
                        )}
                    />
                    <CustomButton
                        loading={isLoading}
                        disabled={isLoading}
                        variants='blue'
                        className="mt-1"
                        type="submit">تبدیل به شبا
                    </CustomButton>
                </form>
            </Form>
            <InquiryPrice price={price}/>
        </>
    );
}
