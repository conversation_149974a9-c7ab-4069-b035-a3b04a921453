import {
    BankInquiryType,
    BaseParamsType,
    GenericResponse,
    IInquiryService, InquiryStatus, InquiryTypeEnum, JibitFailedResultType,
    ProviderType,
    ServiceResult
} from "@/lib/types";
import {ClientSession} from "mongoose";
import inquiryRepository from "@/features/inquiry/inquiry.repository";
import {InternalServerError} from "@/lib/error";
import {handleInquiryServiceError} from "@/utils/helpers-server";
import JibitFetchClient from "@/features/jibit/jibit.fetchClient";

type DepositToIbanInput = {
    depositNumber: string,
    bank: string
} & BaseParamsType

class DepositToIbanService implements IInquiryService<DepositToIbanInput, ServiceResult | undefined> {

    async lookup({
                     bank,
                     depositNumber,
                     validPeriod,
                     userId,
                     source,
                     phone,
                     balance,
                     balance_after
                 }: DepositToIbanInput, session?: ClientSession | null): Promise<GenericResponse<any, undefined>> {
        try {
            const resultFromDb = await inquiryRepository.findOneJibitDepositToIban({
                depositNumber,
                bank,
                source,
                requireValidExpireTime: false
            }, session);

            const createBaseParams = {
                bank,
                provider: ProviderType.JIBIT,
                depositNumber,
                user: userId,
                source,
                phone,
                inquiryType: InquiryTypeEnum.Jibit_DepositToIban,
                balance
            }

            const foundSuccessResult = resultFromDb && resultFromDb.inquiryResult;
            let response: BankInquiryType | null = null;
            if (!foundSuccessResult) {
                const jibitFetchClient = await JibitFetchClient.create()
                try {
                    response = await jibitFetchClient.convertDepositToIban(bank, depositNumber)
                } catch (error: any) {
                    const errorResult = error?.errors;
                    await inquiryRepository.create<JibitFailedResultType>({
                        ...createBaseParams,
                        inquiryStatus: InquiryStatus.FAILED,
                        inquiryResult: errorResult,
                        paid: false,
                    }, session)
                    return {
                        success: 'FAILED',
                        message: error?.message
                    }
                }

                if (!resultFromDb && !response) {
                    throw new InternalServerError();
                }
            }

            const savedResult = await inquiryRepository.create<BankInquiryType>({
                ...createBaseParams,
                validPeriod,
                inquiryStatus: InquiryStatus.SUCCESS,
                inquiryResult: foundSuccessResult ? (resultFromDb.inquiryResult as BankInquiryType) : (response as BankInquiryType)!,
                paid: true,
                balance_after
            }, session)

            return {
                success: 'SUCCESS',
                data: {
                    id: savedResult!._id.toString(),
                    trackingNumber: savedResult!.trackingNumber!
                },
            };
        } catch (error: any) {
            console.error('خطا در jibit-conversion-query.service.ts', error);
            return handleInquiryServiceError(error);
        }
    }

}

export default DepositToIbanService