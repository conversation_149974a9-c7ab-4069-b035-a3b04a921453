import {
    BankInquiryType,
    BaseParamsType,
    GenericResponse,
    IInquiryService, InquiryStatus, InquiryTypeEnum, JibitFailedResultType,
    ProviderType,
    ServiceResult
} from "@/lib/types";
import {ClientSession} from "mongoose";
import inquiryRepository from "@/features/inquiry/inquiry.repository";
import {InternalServerError} from "@/lib/error";
import {handleInquiryServiceError} from "@/utils/helpers-server";
import JibitFetchClient from "@/features/jibit/jibit.fetchClient";

type CardNumberInput = {
    cardNumber: string
} & BaseParamsType

class CardNumberService implements IInquiryService<CardNumberInput, ServiceResult | undefined> {

    async lookup({
                     cardNumber,
                     userId,
                     validPeriod,
                     phone,
                     source,
                     balance,
                     balance_after
                 }: CardNumberInput, session?: ClientSession | null): Promise<GenericResponse<any, undefined>> {

        const creatBaseParams = {
            provider: ProviderType.JIBIT,
            cardNumber,
            user: userId,
            source,
            phone,
            inquiryType: InquiryTypeEnum.Jibit_CardInquiry,
            balance
        }

        try {
            const jibitFetchClient = await JibitFetchClient.create()
            let response: BankInquiryType | null = null;
            try {
                response = await jibitFetchClient.cardNumber(cardNumber);
            } catch (error: any) {
                const errorResult = error?.errors;
                await inquiryRepository.create<JibitFailedResultType>({
                    ...creatBaseParams,
                    inquiryStatus: InquiryStatus.FAILED,
                    inquiryResult: errorResult,
                    paid: false,
                }, session)
                return {
                    success: 'FAILED',
                    message: error?.message
                }
            }

            if (!response) {
                throw new InternalServerError();
            }

            const savedResult = await inquiryRepository.create<BankInquiryType>({
                ...creatBaseParams,
                validPeriod,
                inquiryStatus: InquiryStatus.SUCCESS,
                inquiryResult: response as BankInquiryType,
                paid: true,
                balance_after
            }, session)

            return {
                success: 'SUCCESS',
                data: {
                    id: savedResult!._id.toString(),
                    trackingNumber: savedResult!.trackingNumber!
                },
            };
        } catch (error: any) {
            console.error('خطا در jibit-conversion-query.service.ts', error);
            return handleInquiryServiceError(error, 'jibit-conversion-query.service.ts');
        }
    }

}

export default CardNumberService