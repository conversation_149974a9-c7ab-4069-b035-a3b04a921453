'use client'

import {ChevronDown, History, LogOut, User, WalletCards} from "lucide-react";
import React, {ReactNode} from "react";
import Link from "next/link";
import {useRouter} from "next/navigation";
import {Role, UserPayload} from "@/lib/types";
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from "../ui/dropdown-menu";
import {logOut} from "@/features/auth/auth.action";
import {DASHBOARD_PATH, DEPO_PATH, HISTORY_PATH} from "@/lib/routes";
import {formatWithComma} from "@/utils/helpers";

type DROPDOWNMENU = {
    title: string,
    icon: ReactNode,
    isAdmin: boolean,
    link?: string,
    onClick?: () => void
}[]

type Props = {
    logOutUser: () => void
    user: UserPayload
}


export default function UserLoggedInButton({user, logOutUser}: Props) {
    const dropDownMenuItems: DROPDOWNMENU = [
        {
            title: 'افزایش اعتبار',
            icon: <WalletCards/>,
            link: DEPO_PATH,
            isAdmin: false
        },
        {
            title: 'استعلامات اخیر',
            icon: <History/>,
            link: HISTORY_PATH,
            isAdmin: false
        },
        {
            title: 'پنل مدیریت',
            icon: <History/>,
            link: DASHBOARD_PATH,
            isAdmin: true
        },
        {
            title: 'خروج',
            link: '#',
            icon: <LogOut/>,
            onClick: handleLogout,
            isAdmin: false
        }
    ]
    const router = useRouter()

    async function handleLogout() {
        await logout();
    }


    async function logout() {
        await logOut()
        logOutUser()
        router.refresh()
    }

    return (
        <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild>
                <span>
              <div className="flex min-w-[80px] min-h-[40px] cursor-pointer border border-primary/50
                   bg-primary/10 rounded-[99px] p-[5px] gap-1 items-center justify-center">
                   <ChevronDown size={15}/>
                    <div className="text-left flex justify-center items-end gap-1 flex-col">
                        <span className="font-bold text-xs text-primary/80">{user?.phone}</span>
                        <span className="flex justify-end items-center gap-1">
                            <span className="text-xs font-base font-bold">
                                <span
                                    className='text-xs mx-1'> تومان
                                </span>
                                {formatWithComma(user.balance.toString())}
                            </span>
                        </span>
                    </div>
                     <User size={25}/>
                </div>
                </span>
            </DropdownMenuTrigger>
            <DropdownMenuContent className='w-[180px] z-[900]'>
                {
                    dropDownMenuItems
                        .filter((item) => !item.isAdmin || item.isAdmin === user.admin)
                        .map((item, index) => (
                            <DropdownMenuItem key={index} asChild>
                                <Link
                                    href={item.link || ''}
                                    className='flex gap-2 items-center justify-start h-[35px] cursor-pointer'
                                    onClick={item?.onClick}
                                >
                                    {item.icon}
                                    <span>{item.title}</span>
                                </Link>
                            </DropdownMenuItem>
                        ))
                }
            </DropdownMenuContent>
        </DropdownMenu>
    );
}