import {banks} from "@/lib/data/jibit-bank-list";
import {Card, CardContent} from "@/components/ui/card";
import {InquiryTypeEnum, JibitResultType} from "@/lib/types";
import {translateBankInfoStatus} from "@/utils/helpers";

type Props = {
    inquiry: JibitResultType
}

const inquiryTypeToFarsi = (type: InquiryTypeEnum) => {
    switch (type) {
        case InquiryTypeEnum.Jibit_CardToDeposit:
            return 'استعلام کارت به حساب'
        case InquiryTypeEnum.Jibit_DepositToIban:
            return 'استعلام حساب به شبا'
        case InquiryTypeEnum.Jibit_CardToIban:
            return 'استعلام کارت به شبا'
        case InquiryTypeEnum.Jibit_IbanInquiry:
            return 'استعلام شبا'
        case InquiryTypeEnum.Jibit_CardInquiry:
            return 'استعلام کارت'
        default:
            return '-'
    }
}

export default function JibitBankInquiryHistoryContent({inquiry}: Props) {
    const bankInfo = banks.find((item) => item.bankId === inquiry.bank)
    const owners = inquiry.owners.reduce((prevOwner, currentOwner, index) => {
        let text = prevOwner + currentOwner
        if (index !== inquiry.owners.length - 1) {
            text += ', '
        }
        return text
    }, '')
    return ((
        <div className="mb-4">
            <Card className="overflow-hidden py-0 gap-y-4">
                <div className="flex px-4 py-2 bg-neutral-100 items-center justify-between">
                    <h3 className="text-base font-semibold text-neutral-500">{inquiryTypeToFarsi(inquiry.inquiryType)}</h3>
                </div>
                <CardContent className=" px-2 pb-4">
                    <div className="grid gap-2 text-right">

                        <div className="grid gap-2 px-2">
                            {bankInfo?.bankNameFarsi &&
                                <div className="flex items-center justify-between">
                                                        <span
                                                            className="font-medium text-base">نام بانک:</span>
                                    <div className='flex items-center gap-x-1'>
                                        <span>{bankInfo.bankNameFarsi}</span>
                                        {bankInfo?.iconSmall}
                                    </div>
                                </div>}
                            {owners && <div className="flex justify-between">
                                <span className="font-medium">نام صاحب حساب:</span>
                                <span>{owners}</span>
                            </div>}
                            {inquiry?.ownerName && <div className="flex justify-between">
                                <span className="font-medium">صاحب حساب:</span>
                                <span
                                    dir="ltr" className="text-left">{inquiry.ownerName}</span>
                            </div>}
                            {inquiry?.depositNumber && <div className="flex justify-between">
                                <span className="font-medium">شماره حساب:</span>
                                <span dir="ltr" className="text-left">
                                                        {inquiry.depositNumber}
                                                    </span>
                            </div>}
                            {inquiry?.status && <div className="flex justify-between">
                                <span className="font-medium">وضعیت حساب:</span>
                                <span dir="ltr" className="text-left">
                                                        {translateBankInfoStatus(inquiry?.status)}
                                                    </span>
                            </div>}
                            {inquiry?.iban && <div className="flex justify-between">
                                <span className="font-medium">شماره شبا:</span>
                                <span dir="ltr" className="text-left">
                                                        {inquiry.iban}
                                                    </span>
                            </div>}
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    ))
}
