import {BaseParamsType, GenericResponse, InquiryType, InquiryTypeEnum, ServiceResult} from "@/lib/types";
import {ClientSession} from "mongoose";
import jibitService from "@/features/jibit/jibit.service";
import ghabzinoService from "@/features/ghabzino/ghabzino.service";
import GhabzinoService from "@/features/ghabzino/ghabzino.service";

export async function handleInquiryLookup(inquiry: InquiryType, baseParams: BaseParamsType, session: ClientSession | null = null) {
    const {
        inquiryType,
        sayadId,
        cardNumber,
        depositNumber,
        bank,
        iban,
        accountTypeName
    } = inquiry;
    let inquiryResult: GenericResponse<ServiceResult | undefined> | undefined;
    switch (inquiryType) {
        case InquiryTypeEnum.Jibit_CardToDeposit:
            const cardToDepositInquiry = await jibitService.cardToDeposit()
            inquiryResult = await cardToDepositInquiry.lookup({
                cardNumber: cardNumber!,
                ...baseParams,
            }, session);
            break;
        case InquiryTypeEnum.Jibit_CardToIban:
            const cardToIbanInquiry = await jibitService.cardToIban()
            inquiryResult = await cardToIbanInquiry.lookup({
                cardNumber: cardNumber!,
                ...baseParams,
            }, session);
            break;
        case InquiryTypeEnum.Jibit_DepositToIban:
            const jibitDepositToIbanInquiry = await jibitService.depositToIban()
            inquiryResult = await jibitDepositToIbanInquiry.lookup({
                bank: bank!,
                depositNumber: depositNumber!,
                ...baseParams,
            }, session);
            break;
        case InquiryTypeEnum.Ghanzino_DepositToIban:
            const ghabzinoDepositToIbanInquiry = await ghabzinoService.depositToIban()
            inquiryResult = await ghabzinoDepositToIbanInquiry.lookup({
                accountTypeName: accountTypeName!,
                bank: bank!,
                depositNumber: depositNumber!,
                ...baseParams,
            }, session);
            break;
        case InquiryTypeEnum.Ghanzino_CardToIban:
            const ghabzinoCardToIbanInquiry = await ghabzinoService.cardToIban()
            inquiryResult = await ghabzinoCardToIbanInquiry.lookup({
                cardNumber: cardNumber!,
                accountTypeName: accountTypeName!,
                ...baseParams,
            }, session);
            break;
        case InquiryTypeEnum.Jibit_IbanInquiry:
            const ibanInquiry = await jibitService.iban()
            inquiryResult = await ibanInquiry.lookup({
                iban: iban!,
                ...baseParams,
            }, session);
            break;
        case InquiryTypeEnum.Jibit_CardInquiry:
            const cardInquiry = await jibitService.cardNumber()
            inquiryResult = await cardInquiry.lookup({
                cardNumber: cardNumber!,
                ...baseParams,
            }, session)
            break;
        case InquiryTypeEnum.Ghabzino_SayadCheckInquiry:
            const sayadCheckInquiry = await GhabzinoService.sayadi()
            inquiryResult = await sayadCheckInquiry.lookup({
                sayadId: sayadId!,
                ...baseParams,
            }, session);
            break;
        case InquiryTypeEnum.Ghanzino_KhalafiKhodro:
        case InquiryTypeEnum.Ghanzino_KhalafiMotor:
            const {withDetails, middle, right, alphabet, left, phoneNumber, nationalCode, isMotor} = inquiry
            const trafficFinesInquiry = await GhabzinoService.trafficFines()
            inquiryResult = await trafficFinesInquiry.lookup({
                Left: left!,
                Right: right!,
                Mid: middle,
                Alphabet: alphabet,
                NationalID: nationalCode,
                MobileNumber: phoneNumber,
                isMotor: isMotor === 'true',
                withDetails: withDetails === 'true',
                inquiryType,
                ...baseParams,
            }, session)
            break;
    }

    return inquiryResult;
}