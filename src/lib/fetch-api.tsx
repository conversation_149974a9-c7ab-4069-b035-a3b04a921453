import {AUTHORIZATION, JWT_TOKEN_TYPE} from "@/lib/constants";
import {InternalServerError, TimeoutError} from "@/lib/error";

class FetchApi {
    private customHeaders: Record<string, string> = {};

    constructor(private baseUrl: string = '', private token?: string) {
    }

    setToken(token: string | undefined): void {
        this.token = token;
    }

    setHeader(key: string, value: string): void {
        this.customHeaders[key] = value;
    }

    setHeaders(headers: Record<string, string>): void {
        Object.assign(this.customHeaders, headers);
    }

    async get<T>(url: string, options: RequestInit = {}): Promise<T> {
        return this.request<T>(url, {...options, method: 'GET'});
    }

    async post<T>(url: string, data: unknown, options: RequestInit = {}): Promise<T> {
        return this.request<T>(url, {...options, method: 'POST', body: JSON.stringify(data)});
    }

    async put<T>(url: string, data: unknown, options: RequestInit = {}): Promise<T> {
        return this.request<T>(url, {...options, method: 'PUT', body: JSON.stringify(data)});
    }

    async delete<T>(url: string, options: RequestInit = {}): Promise<T> {
        return this.request<T>(url, {...options, method: 'DELETE'});
    }

    private async request<T>(
        url: string,
        options: RequestInit = {},
        retries: number = 3,
        baseDelay: number = 1000,
        timeoutMs: number = 60000
    ): Promise<T> {
        const headers: any = {
            'Content-Type': 'application/json',
            'accept': 'application/json',
            ...(options.headers || {}),
            ...this.customHeaders,
        };

        if (this.token) {
            headers[AUTHORIZATION] = `${JWT_TOKEN_TYPE} ${this.token}`;
        }

        const fullUrl = this.baseUrl ? `${this.baseUrl}${url}` : url;

        for (let attempt = 1; attempt <= retries; attempt++) {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

            try {
                const response = await fetch(fullUrl, {
                    ...options,
                    headers,
                    signal: controller.signal,
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    let errorMessage = `درخواست با خطای وضعیت ${response.status} مواجه شد.`;
                    console.log(errorMessage);
                    let errors: any = undefined;

                    try {
                        const errorData = await response.json();
                        errorMessage = errorData?.message || errorData?.errors?.message || errorMessage;
                        errors = errorData;
                    } catch (_) {
                        // Handle case where response is not JSON
                    }

                    console.log(errorMessage);
                    throw new InternalServerError(errorMessage, errors);
                }

                const data: T = await response.json();
                return data;
            } catch (error: any) {
                clearTimeout(timeoutId);

                if (error.name === 'AbortError') {
                    const timeoutMessage = 'درخواست به دلیل پایان زمان مجاز (timeout) قطع شد';
                    console.error(timeoutMessage);
                    throw new TimeoutError(timeoutMessage);
                }

                if (attempt === retries) {
                    console.error(`درخواست پس از ${retries} تلاش ناموفق بود:`, error);
                    throw error;
                }

                const delay = baseDelay * Math.pow(2, attempt - 1);
                console.warn(`تلاش ${attempt} ناموفق بود. تلاش دوباره در ${delay} میلی‌ثانیه...`);

                await this.sleep(delay);
            }
        }

        throw new InternalServerError("خطای غیرمنتظره‌ای رخ داده است.");
    }

    private sleep(ms: number): Promise<void> {
        return new Promise((resolve) => setTimeout(resolve, ms));
    }
}

export default FetchApi;