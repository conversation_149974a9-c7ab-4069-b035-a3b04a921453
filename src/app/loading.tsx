import {Spinner} from "@/components/common/spinner";
import {cn} from "@/lib/utils";

type LoadingProps = {
    className?: string;
    fullHeight?: boolean; // تنظیم ارتفاع کامل
};

export default function Loading({
                                    className,
                                    fullHeight = true, // پیش‌فرض: false
                                }: LoadingProps) {
    return (
        <div
            className={cn(
                "flex flex-col justify-center items-center gap-y-1 w-full",
                fullHeight ? "h-[80vh]" : "h-full",
                className
            )}
        >
            <Spinner className="text-primary" size="large"/>
            <span className="text-xs">لطفا منتظر بمانید</span>
        </div>
    );
}