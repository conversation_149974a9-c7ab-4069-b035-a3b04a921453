import {NextRequest, NextResponse} from 'next/server'
import {NextURL} from 'next/dist/server/web/next-url'
import {AUTHORIZATION, RETURN_URL, X_SOURCE} from '@/lib/constants'
import {
    adminRoutes,
    DASHBOARD_PATH,
    DASH<PERSON>ARD_SERVICE_PATH,
    HOME_PATH,
    LOGIN_PATH,
    protectedRoutes,
    signOutOnlyRoutes
} from '@/lib/routes'
import jwtToken from '@/lib/services/token.service'
import cookieService from '@/lib/services/cookie.service'
import {JWTPayload} from 'jose'
import {JWTUserPayload} from '@/lib/types'

export default async function middleware(req: NextRequest) {
    const jwt_token = req.cookies.get(AUTHORIZATION)
    const url = req.nextUrl
    const path = url.pathname

    const host = req.headers.get('x-forwarded-host') || req.headers.get('host') || ''

    const source = host.toLowerCase()

    const isProtectedRoute = protectedRoutes.some((pathname) => path.startsWith(pathname))
    const isShownOnlyWhenSignOut = signOutOnlyRoutes.includes(path)
    const isAdminRoute = adminRoutes.some((adminPath) => path.startsWith(adminPath))

    let jwtPayload: JWTPayload | null = null
    if (jwt_token?.value) {
        jwtPayload = await jwtToken.verifyJwtToken<JWTUserPayload>(jwt_token.value.split(' ')[1])
    }

    const isAuthorized = !!jwtPayload

    if (!isAuthorized) {
        await cookieService.deleteAuthorizationToken()
    }

    if (isProtectedRoute && !isAuthorized) {
        return redirectToLogin(url, source)
    }

    if (isShownOnlyWhenSignOut && isAuthorized) {
        return redirectToHome(url, source)
    }

    if (isAdminRoute && path === DASHBOARD_PATH) {
        if (jwtPayload?.admin) {
            const adminServicesUrl = new URL(DASHBOARD_SERVICE_PATH, url.origin)
            const res = NextResponse.redirect(adminServicesUrl)
            res.headers.set(X_SOURCE, source)
            return res
        } else {
            return redirectToHome(url, source)
        }
    }

    const res = NextResponse.next()
    res.headers.set(X_SOURCE, source)
    return res
}

function redirectToHome(url: NextURL, source: string) {
    const homeUrl = new URL(HOME_PATH, url.origin)
    const res = NextResponse.redirect(homeUrl)
    res.headers.set(X_SOURCE, source)
    return res
}

function redirectToLogin(url: NextURL, source: string) {
    const loginUrl = new URL(LOGIN_PATH, url.origin)
    loginUrl.searchParams.set(RETURN_URL, url.pathname + url.search)
    const res = NextResponse.redirect(loginUrl)
    res.headers.set(X_SOURCE, source)
    return res
}

export const config = {
    matcher: ['/((?!api|_next/static|_next/image|.*\\.png$).*)'],
}