import {IInquiryResult, InquiryServiceCreateInput, InquiryStatus, InquiryTypeEnum, ProviderType} from "@/lib/types";
import {addHoursToNow, generateTrackingNumber} from "@/utils/helpers-server";
import inquiryModel from "@/features/inquiry/inquiry.schema";
import {ClientSession} from "mongoose";
import userRepository from "@/features/user/user.repository";

class InquiryRepository {

    async findByTrackingNumber({trackingNumber, source}: {
        trackingNumber: string,
        source: string
    }, session: ClientSession | null = null): Promise<IInquiryResult | null> {
        return inquiryModel.findOne({trackingNumber, source}).session(session);
    }

    async findInquiriesByUserIdIfNotExpired({userId, source}: {
        userId: string,
        source: string
    }, session: ClientSession | null = null): Promise<IInquiryResult[]> {

        const currentTime = new Date();
        return inquiryModel.find({
            user: userId,
            inquiryStatus: InquiryStatus.SUCCESS,
            trackingNumberExpireTime: {$gt: currentTime},
            source,
        }).sort({_id: -1})
            .session(session);
    }

    async findOneJibitCardToIban({cardNumber, source, phone, requireValidExpireTime = true}: {
                                     cardNumber: string,
                                     phone?: string,
                                     source: string,
                                     requireValidExpireTime?: boolean
                                 }, session: ClientSession | null = null
    ): Promise<IInquiryResult | null> {

        const query: any = {
            cardNumber,
            inquiryStatus: InquiryStatus.SUCCESS,
            inquiryType: InquiryTypeEnum.Jibit_CardToIban,
            provider: ProviderType.JIBIT,
            source
        };

        if (phone) query.phone = phone;
        if (requireValidExpireTime) query.trackingNumberExpireTime = {$gt: new Date()};

        return inquiryModel.findOne(query).sort({createdAt: -1}).session(session);
    }

    async findOneJibitCardToDeposit(
        {phone, requireValidExpireTime = false, cardNumber, source}:
        {
            cardNumber: string,
            phone?: string,
            source?: string,
            requireValidExpireTime?: boolean
        }
        , session: ClientSession | null = null
    ): Promise<IInquiryResult | null> {
        const query: any = {
            cardNumber,
            inquiryStatus: InquiryStatus.SUCCESS,
            inquiryType: InquiryTypeEnum.Jibit_CardToDeposit,
            provider: ProviderType.JIBIT,
        };

        if (phone) query.phone = phone;
        if (source) query.source = source;
        if (requireValidExpireTime) query.trackingNumberExpireTime = {$gt: new Date()};

        return inquiryModel.findOne(query).sort({createdAt: -1}).session(session);
    }

    async findOneJibitDepositToIban(
        {
            depositNumber,
            bank,
            phone,
            source,
            requireValidExpireTime = true,
        }: {
            depositNumber: string,
            bank: string,
            phone?: string
            source?: string
            requireValidExpireTime?: boolean
        }, session: ClientSession | null = null
    ): Promise<IInquiryResult | null> {
        const query: any = {
            depositNumber,
            bank,
            inquiryStatus: InquiryStatus.SUCCESS,
            inquiryType: InquiryTypeEnum.Jibit_DepositToIban,
            provider: ProviderType.JIBIT,
        };

        if (phone) query.phone = phone;
        if (source) query.source = source;

        if (requireValidExpireTime) {
            query.trackingNumberExpireTime = {$gt: new Date()};
        }

        return inquiryModel.findOne(query).sort({createdAt: -1}).session(session);
    }

    async findOneGhabzinoDepositToIban(
        {
            depositNumber,
            bank,
            accountTypeName,
            phone,
            source,
            requireValidExpireTime = true,
        }: {
            depositNumber: string,
            bank: string,
            accountTypeName: string,
            phone?: string
            source?: string
            requireValidExpireTime?: boolean
        }, session: ClientSession | null = null
    ): Promise<IInquiryResult | null> {
        const query: any = {
            depositNumber,
            bank,
            accountTypeName,
            inquiryStatus: InquiryStatus.SUCCESS,
            inquiryType: InquiryTypeEnum.Ghanzino_DepositToIban,
            provider: ProviderType.GHABZINO,
        };

        if (phone) query.phone = phone;
        if (source) query.source = source;

        if (requireValidExpireTime) {
            query.trackingNumberExpireTime = {$gt: new Date()};
        }

        return inquiryModel.findOne(query).sort({createdAt: -1}).session(session);
    }

    async findOneGhabzinoCardToIban(
        {
            cardNumber,
            accountTypeName,
            phone,
            source,
            requireValidExpireTime = true,
        }: {
            cardNumber: string,
            accountTypeName: string,
            phone?: string
            source?: string
            requireValidExpireTime?: boolean
        }, session: ClientSession | null = null
    ): Promise<IInquiryResult | null> {
        const query: any = {
            cardNumber,
            accountTypeName,
            inquiryStatus: InquiryStatus.SUCCESS,
            inquiryType: InquiryTypeEnum.Ghanzino_CardToIban,
            provider: ProviderType.GHABZINO,
        };

        if (phone) query.phone = phone;
        if (source) query.source = source;

        if (requireValidExpireTime) {
            query.trackingNumberExpireTime = {$gt: new Date()};
        }

        return inquiryModel.findOne(query).sort({createdAt: -1}).session(session);
    }

    async findOneJibitInquireIban(
        {
            iban,
            phone,
            source,
            requireValidExpireTime = true,
        }: {
            iban: string,
            phone?: string
            source?: string,
            requireValidExpireTime?: boolean
        }, session: ClientSession | null = null
    ): Promise<IInquiryResult | null> {

        const query: any = {
            iban,
            inquiryStatus: InquiryStatus.SUCCESS,
            inquiryType: InquiryTypeEnum.Jibit_IbanInquiry,
            provider: ProviderType.JIBIT,
        };

        if (phone) query.phone = phone;
        if (source) query.source = source;

        if (requireValidExpireTime) {
            query.trackingNumberExpireTime = {$gt: new Date()};
        }

        return inquiryModel.findOne(query).sort({createdAt: -1}).session(session);
    }

    async findOneJibitInquireCard(
        {
            cardNumber,
            phone,
            source,
            requireValidExpireTime = true,
        }: {
            cardNumber: string,
            phone?: string,
            source?: string,
            requireValidExpireTime?: boolean
        }, session: ClientSession | null = null
    ): Promise<IInquiryResult | null> {

        const query: any = {
            cardNumber,
            inquiryStatus: InquiryStatus.SUCCESS,
            inquiryType: InquiryTypeEnum.Jibit_CardInquiry,
            provider: ProviderType.JIBIT,
        };

        if (phone) query.phone = phone;
        if (source) query.source = source;

        if (requireValidExpireTime) {
            query.trackingNumberExpireTime = {$gt: new Date()};
        }

        return inquiryModel.findOne(query).sort({createdAt: -1}).session(session);
    }

    async findOneGhabzinoInquireSayadId(
        {
            sayadId,
            phone,
            source,
            requireValidExpireTime = true,
        }: {
            sayadId: string,
            phone?: string,
            source?: string,
            requireValidExpireTime?: boolean
        }, session: ClientSession | null = null
    ): Promise<IInquiryResult | null> {

        const query: any = {
            sayadId,
            inquiryStatus: InquiryStatus.SUCCESS,
            inquiryType: InquiryTypeEnum.Ghabzino_SayadCheckInquiry,
            provider: ProviderType.GHABZINO,
        };

        if (phone) query.phone = phone;
        if (source) query.source = source;

        if (requireValidExpireTime) {
            query.trackingNumberExpireTime = {$gt: new Date()};
        }

        return inquiryModel.findOne(query).sort({createdAt: -1}).session(session);
    }

    async findOneGhabzinoTrafficFineInquiry({
                                                inquiryType,
                                                nationalID,
                                                withDetails,
                                                plaque,
                                                source,
                                                phoneNumber,
                                                phone,
                                                inquiryStatus = InquiryStatus.SUCCESS,
                                                requireValidExpireTime = true,
                                            }: {
        plaque: string,
        nationalID?: string | null,
        phoneNumber?: string | null,
        withDetails: boolean,
        inquiryStatus: InquiryStatus,
        phone: string,
        source: string,
        inquiryType: InquiryTypeEnum,
        requireValidExpireTime?: boolean
    }, session: ClientSession | null = null): Promise<IInquiryResult | null> {

        const query: any = {
            inquiryType,
            inquiryStatus,
            phone,
            source,
            withDetails,
            plaque,
        }

        if (withDetails) {
            query.inquiry_nationalId = nationalID;
            query.inquiry_mobileNumber = phoneNumber;
        }

        if (requireValidExpireTime) {
            query.trackingNumberExpireTime = {$gt: new Date()};
        }

        return inquiryModel.findOne(query).sort({createdAt: -1}).session(session);
    }

    async create<T1, T2 = undefined>(data: InquiryServiceCreateInput<T1, T2>, session: ClientSession | null = null): Promise<IInquiryResult | null> {

        const {validPeriod = 0} = data;
        const {ip, userAgent} = await userRepository.getUserAgent()
        const newJibitResult = new inquiryModel({
            source: data.source,
            trackingNumber: generateTrackingNumber(),
            trackingNumberExpireTime: addHoursToNow(validPeriod),
            provider: data.provider,
            sayadId: data?.sayadId,
            plaque: data?.plaque,
            inquiry_nationalId: data?.inquiry_nationalId,
            inquiry_mobileNumber: data?.inquiry_mobileNumber,
            ip,
            withDetails: data.withDetails,
            paid: data.paid,
            iban: data?.iban,
            agent: userAgent,
            user: data.user,
            depositNumber: data?.depositNumber,
            bank: data?.bank,
            cardNumber: data?.cardNumber,
            accountTypeName: data?.accountTypeName,
            phone: data.phone,
            inquiryType: data?.inquiryType,
            inquiryResult: data?.inquiryResult,
            inquiryParams: data?.inquiryParams,
            inquiryStatus: data.inquiryStatus,
            balance: data?.balance,
            balance_after: data?.balance_after,
        });

        return newJibitResult.save({session});

    }

    async findByIdAndUpdateBalance({balance, balance_after, id}: {
        id: string, balance: number,
        balance_after: number
    }, session: ClientSession | null = null) {
        return inquiryModel.findOneAndUpdate(
            {_id: id},
            {$set: {balance, balance_after}},
            {
                session: session ?? undefined, // Only pass if not null
                new: true, // Return the updated document
            }
        );
    }
}

export default new InquiryRepository()