import {
    GhabzinoResponse,
    IInquiryResult,
    InquiryType,
    InquiryTypeEnum,
    VehiclePaymentDetails
} from "@/lib/types";
import {convertRialToToman, mapBankCodes, miladiToShamsi} from "@/utils/helpers-server";

import ghabzinoService from "@/features/ghabzino/ghabzino.service";
import {ValidationError} from "@/lib/error";

export default function handleInquiryValues(params: InquiryType) {
    switch (params.inquiryType) {
        case InquiryTypeEnum.Ghanzino_DepositToIban:
            //conver jibit bank to ghabzino bank Id
            const ghabzinoBankId = mapBankCodes(params.bank!);
            if (!ghabzinoBankId) {
                throw new ValidationError(["تبدیل از شناسه بانک جیبیت به قبضینو ناموفق بود"])
            }
            params.bank = ghabzinoBankId;
            break
        default:
            break;
    }
}


export async function handleGetTrafficInquiry(inquiryFromDb: IInquiryResult) {
    const inquiry = (inquiryFromDb.inquiryResult as GhabzinoResponse<VehiclePaymentDetails>)
    const parameters = inquiry.Parameters;
    if (parameters) {
        if (inquiryFromDb.withDetails && parameters.Details?.length) {
            const payBillPortalInquiry = await ghabzinoService.payBillPortal()
            const detailsPaymentLinksPromises = parameters.Details.map(async (item) => {
                return payBillPortalInquiry.lookup({
                    phone: inquiryFromDb.phone,
                    billId: item.BillID,
                    paymentId: item.PaymentID,
                })
            })
            const totalAmountPaymentPromise = payBillPortalInquiry.lookup({
                phone: inquiryFromDb.phone,
                paymentId: parameters.PaymentID,
                billId: parameters.BillID,
            })

            const allResults = await Promise.all([
                ...detailsPaymentLinksPromises,
                totalAmountPaymentPromise,
            ]);

            const detailsResults = allResults.slice(0, parameters.Details.length);

            const totalResult = allResults[allResults.length - 1];

            parameters.Details.map((item, index: number) => {
                const linkResult = detailsResults[index];
                item.DateTime = miladiToShamsi(item.DateTime);
                item.Amount = convertRialToToman(item.Amount);
                item.PaymentUrl = linkResult?.success === 'SUCCESS' ? linkResult.data?.PaymentLink || '' : '';
            })
            parameters.paymentUrl = totalResult.success === 'SUCCESS' ? totalResult.data!.PaymentLink : '';
            parameters.TotalAmount = convertRialToToman(parameters.TotalAmount!)

        } else {
            const payBillPortalInquiry = await ghabzinoService.payBillPortal()
            const totalAmountResult = await payBillPortalInquiry.lookup({
                phone: inquiryFromDb.phone,
                paymentId: parameters.PaymentID,
                billId: parameters.BillID,
            })
            parameters.paymentUrl = totalAmountResult.success === 'SUCCESS' ? totalAmountResult.data!.PaymentLink : '';
            parameters.Amount = convertRialToToman(parameters.Amount!)
        }
    }
}