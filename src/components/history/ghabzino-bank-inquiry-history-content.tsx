import {Card, CardContent} from "@/components/ui/card";
import {GhabzinoBankType, InquiryTypeEnum} from "@/lib/types";
import Image from "next/image";
import {banks} from "@/lib/data/ghabzino-bank";

type Props = {
    inquiry: GhabzinoBankType
}

const inquiryTypeToFarsi = (type: InquiryTypeEnum) => {
    switch (type) {
        case InquiryTypeEnum.Ghanzino_DepositToIban:
            return 'استعلام حساب به شبا'
        case InquiryTypeEnum.Ghanzino_CardToIban:
            return 'استعلام کارت به شبا'
    }
}

export default function GhabzinoBankInquiryHistoryContent({inquiry}: Props) {

    if (!inquiry?.bankShowName) {
        const bankName = banks.find(item => item.bank === inquiry.bankName)?.bankNameFarsi
        if (bankName) {
            inquiry.bankShowName = bankName
        }
    }

    return ((
        <div className="mb-4">
            <Card className="overflow-hidden py-0 gap-y-4">
                <div className="flex px-4 py-2 bg-neutral-100 items-center justify-between">
                    <h3 className="text-base font-semibold text-neutral-500">{inquiryTypeToFarsi(inquiry.inquiryType)}</h3>
                </div>
                <CardContent className=" px-2 pb-4">
                    <div className="grid gap-2 text-right">

                        <div className="grid gap-2 px-2">
                            {inquiry?.bankShowName &&
                                <div className="flex items-center justify-between">
                                                        <span
                                                            className="font-medium text-base">نام بانک:</span>
                                    <div className='flex items-center gap-x-1'>
                                        <span>{inquiry?.bankShowName}</span>
                                        {inquiry?.imageUrl &&
                                            <Image src={inquiry.imageUrl} width={50} height={50}
                                                   alt={inquiry?.bankShowName || 'لوگوی بانک'}/>
                                        }
                                    </div>
                                </div>}
                            {inquiry?.cardNumber && <div className="flex justify-between">
                                <span className="font-medium">شماره کارت:</span>
                                <span dir="ltr" className="text-left">
                                                        {inquiry.cardNumber}
                                                    </span>
                            </div>}
                            {inquiry?.depositNumber && <div className="flex justify-between">
                                <span className="font-medium">شماره حساب:</span>
                                <span dir="ltr" className="text-left">
                                                        {inquiry?.depositNumber}
                                                    </span>
                            </div>}
                            {inquiry?.shebaNumber && <div className="flex justify-between">
                                <span className="font-medium">شماره شبا:</span>
                                <span dir="ltr" className="text-left">
                                                        {inquiry.shebaNumber}
                                                    </span>
                            </div>}
                            {inquiry?.description && <div className="flex justify-between">
                                <span dir="ltr" className="text-left">
                                                        {inquiry.description}
                                                    </span>
                            </div>}
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    ))
}
