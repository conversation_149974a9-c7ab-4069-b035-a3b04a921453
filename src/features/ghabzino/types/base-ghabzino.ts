import {IInquiryService} from "@/lib/types";

export abstract class BaseGhabzino {
    abstract sayadi(): Promise<IInquiryService<any, any>>;

    abstract trafficFines(): Promise<IInquiryService<any, any>>;

    abstract payBillPortal(): Promise<IInquiryService<any, any>>;

    abstract depositToIban(): Promise<IInquiryService<any, any>>;

    abstract cardToIban(): Promise<IInquiryService<any, any>>;
}