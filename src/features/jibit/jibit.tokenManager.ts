import tokenService from "@/lib/services/token.service";
import FetchApi from "@/lib/fetch-api";
import envConfig from "@/lib/config.env";

class JibitTokenManager {
    private accessToken: string | null = null;
    private ongoingFetch: Promise<string> | null = null;

    private async fetchToken(): Promise<string> {
        const response = await this.generateAccessToken();
        this.accessToken = response.accessToken;
        return this.accessToken;
    }

    public async getToken(): Promise<string> {
        if (this.accessToken && !tokenService.isTokenExpired(this.accessToken, 5)) {
            return this.accessToken;
        }

        if (!this.ongoingFetch) {
            this.ongoingFetch = this.fetchToken().finally(() => this.ongoingFetch = null);
        }

        return this.ongoingFetch;
    }

    private async generateAccessToken() {

        const env = envConfig()
        const jibitBaseUrl = env.JIBIT.BASE_URL
        const apiKey = env.JIBIT.API_KEY
        const secretKey = env.JIBIT.SECRET_KEY

        const data = {
            apiKey,
            secretKey
        };

        const fetchInstance = new FetchApi(jibitBaseUrl);
        return fetchInstance.post<{ accessToken: string }>('/v1/tokens/generate', data);
    }
}

export default new JibitTokenManager();