import AdminSettingsComponent from "@/components/admin/settings/admin-settings-component";
import {getApplications} from "@/features/application/application.action";
import {redirect} from "next/navigation";
import {ERROR_PATH} from "@/lib/routes";

export default async function SettingsPage() {

    const applications = await getApplications()

    if (!applications.success) {
        redirect(ERROR_PATH)
    }
    return (
        <>
            <AdminSettingsComponent applications={applications.data || []}/>
        </>
    );
}
