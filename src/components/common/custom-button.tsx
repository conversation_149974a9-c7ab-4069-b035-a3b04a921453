import React from "react";
import clsx from "clsx";
import {Spinner} from "@/components/common/spinner";
import Link from "next/link";

interface Props extends React.ButtonHTMLAttributes<HTMLButtonElement> {
    variants?: 'blue' | 'green' | 'red' | 'outline-green' | 'outline-red' | 'black' | 'outline-black' | 'orange' | 'outline-orange';
    loading?: boolean;
    sizeType?: 'normal' | 'slim';
    href?: string;
    children?: React.ReactNode;
    className?: string;
    disabled?: boolean;
    target?: React.HTMLAttributeAnchorTarget
}

const CustomButton = ({
                          loading,
                          sizeType = 'normal',
                          variants = "green",
                          children,
                          className,
                          target,
                          disabled = false,
                          href,
                          ...props // Spread remaining props
                      }: Props) => {
    const baseClasses = "w-full rounded-md py-3 md:py-4 transition-all font-medium text-center inline-flex items-center justify-center cursor-pointer";

    const variantInstances = {
        blue: "bg-blue-500 border border-blue-500 text-white hover:bg-blue-400 hover:bg-blue-400",
        orange: "bg-primary border border-primary text-white hover:bg-primary-80 hover:border-primary-80",
        green: "bg-green-700 border border-green-700 text-white hover:bg-green-800 hover:border-green-800",
        red: "bg-red-500 border border-red-500 text-white hover:bg-red-600 hover:border-red-600",
        black: "bg-black border border-gray-900 text-white hover:bg-gray-800 hover:border-gray-800",
        'outline-black': "bg-white border border-black text-black hover:bg-gray-900 hover:border-gray-900 hover:text-white",
        'outline-orange': "bg-white border border-primary text-primary hover:bg-primary hover:border-primary hover:text-white",
        'outline-red': "bg-white border border-red-500 text-red-500 hover:bg-red-100 hover:border-red-600 hover:text-red-600",
        'outline-green': "bg-white border border-green-500 text-green-500 hover:bg-green-100 hover:border-green-600 hover:text-green-600",
    } as const;

    const sizeInstances = {
        normal: 'py-2',
        slim: 'py-1',
    };

    const disabledClasses = "opacity-50 cursor-not-allowed";

    if (href) {
        return (
            <Link
                href={href}
                target={target}
                className={clsx(baseClasses, variantInstances[variants], sizeInstances[sizeType], className, {
                    [disabledClasses]: disabled,
                })}
            >
                {children}
                {loading && <Spinner size="small" className="text-white"/>}
            </Link>
        );
    }

    return (
        <button
            className={clsx(baseClasses, variantInstances[variants], sizeInstances[sizeType], className, {
                [disabledClasses]: disabled,
            })}
            disabled={disabled}
            {...props} // Spread remaining props after defining disabled
        >
            {children}
            {loading && <Spinner size="small" className="text-white mx-1"/>}
        </button>
    );
};

export default CustomButton;