import {InquiryType, ITransaction} from "@/lib/types";
import transactionModel from "@/features/transaction/transaction.schema";
import envConfig from "@/lib/config.env";
import {ClientSession} from "mongoose";

class TransactionRepository {
    async createTransaction({amount, phone, authority, ip, inquiry, agent, source}: {
        amount: number,
        authority: string,
        agent: string,
        phone: string,
        inquiry?: InquiryType,
        source: string,
        ip: string
    }): Promise<ITransaction> {
        const env = envConfig();
        const isDevelopment = env.NODE_ENV === "development";

        // if (!isDevelopment && !env.ZARINPAL.TERMINAL_ID) {
        //     throw new InternalServerError("TERMINAL_ID خالی است")
        // }

        return transactionModel.create({
            amount,
            terminalId: isDevelopment ? "test" : env.ZARINPAL.TERMINAL_ID,
            authority,
            agent,
            source,
            inquiry,
            phone,
            ip
        });
    }

    async findTransactionByAuthorityAndPhone({authority, phone, source}: {
        authority: string,
        phone: string,
        source: string
    }, session: ClientSession | null = null): Promise<ITransaction | null> {

        return transactionModel.findOne({
            authority,
            phone,
            source,
        }).session(session)
    }
}

export default new TransactionRepository()
