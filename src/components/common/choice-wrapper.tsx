import {cn} from "@/lib/utils";
import {useMemo} from "react";

type Props = {
    selected?: boolean;
    children?: React.ReactNode;
    success?: boolean;
    error?: boolean;
    borderColor?: string;
    backgroundColor?: string;
    className?: string;
    onClick?: () => void;
    variant?: 'normal' | 'info' | 'none'
};

export default function ChoiceWrapper({
                                          selected,
                                          children,
                                          success,
                                          onClick,
                                          error,
                                          borderColor,
                                          backgroundColor,
                                          className,
                                          variant = "none",
                                      }: Props) {
    const background = useMemo(() => {
            if (variant === "normal") {
                return '#F3F3F3'
            } else if (variant === 'info') {
                return '#FFF5D8'
            } else {
                return backgroundColor
            }
        }
        , [variant, backgroundColor]);

    const border = useMemo(() => {
            if (variant === "normal") {
                return '#F3F3F3'
            } else if (variant === 'info') {
                return '#F7BC06'
            } else {
                return borderColor
            }
        }
        , [variant, borderColor]);
    return (
        <div
            onClick={onClick}
            style={{
                backgroundColor: background,
                borderColor: border,
            }}
            className={cn(
                "w-full flex border-1 px-2 md:px-4 rounded-2xl items-center gap-2", className,
                {
                    "border-2 !border-gray-200": selected || success,
                    "!bg-red-100 !border-red-400": error,
                }
            )}
        >
            {children}
        </div>
    );
}
