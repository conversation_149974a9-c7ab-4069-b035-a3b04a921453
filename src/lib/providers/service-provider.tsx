'use client';

import React, {createContext, useEffect, useState, useCallback} from 'react';
import {ServiceType} from '@/lib/types';
import {getOrSeedAppServices} from "@/features/service/service.action";
import toast from "react-hot-toast";

type ServiceContextType = {
    services: ServiceType[];
    isLoading: boolean;
    refetchServices: () => Promise<void>;
};

export const ServiceContext = createContext<ServiceContextType>({
    services: [],
    isLoading: true,
    refetchServices: async () => {
    },
});

type ServiceProviderProps = {
    children: React.ReactNode;
    initialServices?: ServiceType[]; // داده اولیه از سرور
};

export const ServiceProvider = ({children, initialServices = []}: ServiceProviderProps) => {
    const [services, setServices] = useState<ServiceType[]>(initialServices);
    const [isLoading, setIsLoading] = useState(false);

    const fetchServices = useCallback(async () => {
        setIsLoading(true);
        const actionResult = await getOrSeedAppServices();
        if (!actionResult.success) {
            toast.error(actionResult.message!);
        } else {
            setServices(actionResult.data! as any);
        }
        setIsLoading(false);
    }, []);

    useEffect(() => {
        if (initialServices.length === 0) {
            fetchServices();
        }
    }, [fetchServices, initialServices.length]);

    return (
        <ServiceContext.Provider value={{services, isLoading, refetchServices: fetchServices}}>
            {children}
        </ServiceContext.Provider>
    );
};